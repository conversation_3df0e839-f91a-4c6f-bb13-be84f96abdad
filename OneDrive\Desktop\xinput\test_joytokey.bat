@echo off
echo ===================================================
echo JoyToKey Stealth Execution Test
echo ===================================================

echo [+] Testing direct JoyToKey execution...
echo [+] This will launch <PERSON>To<PERSON><PERSON> normally first to verify it works

echo.
echo Press any key to launch <PERSON>T<PERSON><PERSON>ey normally (visible)...
pause >nul

start "" "JoyToKey.exe"

echo [+] JoyToKey should now be running normally
echo [+] Check if the JoyToKey window appeared
echo.
echo Press any key to continue with stealth test...
pause >nul

echo.
echo [+] Now testing stealth execution methods...
echo.

echo Choose stealth method:
echo 1. Native C++ Stealth Loader
echo 2. PowerShell Advanced Stealth
echo 3. Both methods
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" goto native
if "%choice%"=="2" goto powershell
if "%choice%"=="3" goto both
goto end

:native
echo [+] Launching Native C++ Stealth Loader...
echo [+] This should execute JoyToKey in stealth mode
echo.
stealth_loader.exe
goto end

:powershell
echo [+] Launching PowerShell Advanced Stealth...
echo [+] This should execute JoyToKey using WMI stealth
echo.
powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1
goto end

:both
echo [+] Launching both methods...
echo [+] First: Native C++ Stealth Loader
start /b stealth_loader.exe
timeout /t 3 /nobreak >nul
echo [+] Second: PowerShell Advanced Stealth
powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1
goto end

:end
echo.
echo [+] Test completed
echo [+] Check Task Manager to see if JoyToKey processes are running
echo [+] Look for processes like:
echo     - JoyToKey.exe (normal execution)
echo     - notepad.exe (process hollowing)
echo     - svchost_*.exe (obfuscated names)
echo.
echo Press any key to exit...
pause >nul
