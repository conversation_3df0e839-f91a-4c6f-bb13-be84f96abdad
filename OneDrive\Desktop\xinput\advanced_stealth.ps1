# Advanced Stealth Execution Framework
# Security Research Tool - Use Responsibly

param(
    [string]$TargetBinary = "JoyToKey.exe",
    [string]$OutputDir = "stealth_output"
)

# Advanced anti-analysis techniques
function Test-AnalysisEnvironment {
    $indicators = @()
    
    # Check for debugging tools
    $debuggers = @("ollydbg", "x64dbg", "windbg", "ida", "ghidra", "processhacker")
    foreach ($debugger in $debuggers) {
        if (Get-Process -Name $debugger -ErrorAction SilentlyContinue) {
            $indicators += "Debugger detected: $debugger"
        }
    }
    
    # Check for VM artifacts using WMI
    $bios = Get-WmiObject -Class Win32_BIOS
    $vmIndicators = @("VBOX", "VMWARE", "QEMU", "VIRTUAL", "XEN")
    foreach ($indicator in $vmIndicators) {
        if ($bios.Manufacturer -like "*$indicator*" -or $bios.Version -like "*$indicator*") {
            $indicators += "VM detected: $indicator"
        }
    }
    
    # Check for sandbox artifacts
    $sandboxProcesses = @("vmsrvc", "vboxtray", "vmtoolsd", "sandboxie")
    foreach ($proc in $sandboxProcesses) {
        if (Get-Process -Name $proc -ErrorAction SilentlyContinue) {
            $indicators += "Sandbox detected: $proc"
        }
    }
    
    # Check system uptime (sandboxes often have low uptime)
    $uptime = (Get-WmiObject -Class Win32_OperatingSystem).LastBootUpTime
    $bootTime = [Management.ManagementDateTimeConverter]::ToDateTime($uptime)
    $uptimeHours = ((Get-Date) - $bootTime).TotalHours
    
    if ($uptimeHours -lt 1) {
        $indicators += "Suspicious uptime: $([math]::Round($uptimeHours, 2)) hours"
    }
    
    return $indicators
}

# Generate random legitimate-looking process name
function Get-RandomProcessName {
    $legitimateNames = @(
        "dwm", "winlogon", "csrss", "lsass", "services", 
        "svchost", "explorer", "smss", "wininit", "spoolsv"
    )
    
    $baseName = Get-Random -InputObject $legitimateNames
    $suffix = -join ((1..4) | ForEach-Object { Get-Random -InputObject @('0'..'9' + 'a'..'z') })
    
    return "$baseName`_$suffix.exe"
}

# Advanced binary obfuscation
function Invoke-BinaryObfuscation {
    param([string]$BinaryPath, [string]$OutputPath)
    
    try {
        $originalBytes = [System.IO.File]::ReadAllBytes($BinaryPath)
        $obfuscatedBytes = $originalBytes.Clone()
        
        # XOR encryption with dynamic key
        $key = [System.Text.Encoding]::UTF8.GetBytes("DynamicKey$(Get-Date -Format 'yyyyMMdd')")
        for ($i = 0; $i -lt $obfuscatedBytes.Length; $i++) {
            $obfuscatedBytes[$i] = $obfuscatedBytes[$i] -bxor $key[$i % $key.Length]
        }
        
        # Add entropy padding
        $padding = New-Object byte[] (Get-Random -Minimum 1024 -Maximum 4096)
        (New-Object System.Random).NextBytes($padding)
        $obfuscatedBytes += $padding
        
        [System.IO.File]::WriteAllBytes($OutputPath, $obfuscatedBytes)
        return $true
    }
    catch {
        Write-Error "Obfuscation failed: $_"
        return $false
    }
}

# Registry manipulation for stealth
function Set-StealthRegistryEntries {
    param([string]$FakeName, [string]$RealPath)
    
    try {
        # Create fake application entries
        $appPathKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\$FakeName"
        New-Item -Path $appPathKey -Force | Out-Null
        Set-ItemProperty -Path $appPathKey -Name "(Default)" -Value $RealPath
        
        # Create fake uninstall entry
        $uninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$($FakeName.Replace('.exe', ''))"
        New-Item -Path $uninstallKey -Force | Out-Null
        Set-ItemProperty -Path $uninstallKey -Name "DisplayName" -Value "Windows System Component"
        Set-ItemProperty -Path $uninstallKey -Name "Publisher" -Value "Microsoft Corporation"
        Set-ItemProperty -Path $uninstallKey -Name "InstallLocation" -Value (Split-Path $RealPath)
        
        return $true
    }
    catch {
        Write-Error "Registry manipulation failed: $_"
        return $false
    }
}

# Process hollowing using PowerShell and .NET
function Invoke-ProcessHollowing {
    param([string]$TargetProcess, [byte[]]$Payload)
    
    try {
        # Create suspended process using .NET
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = $TargetProcess
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Hidden
        $startInfo.CreateNoWindow = $true
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        
        if ($process) {
            # Suspend the process (simplified approach)
            $processHandle = $process.Handle
            
            # In a full implementation, this would involve:
            # 1. Getting thread context
            # 2. Unmapping original image
            # 3. Allocating new memory
            # 4. Writing payload
            # 5. Updating entry point
            # 6. Resuming execution
            
            Write-Host "Process hollowing initiated for PID: $($process.Id)"
            return $process.Id
        }
    }
    catch {
        Write-Error "Process hollowing failed: $_"
        return $null
    }
}

# WMI-based execution for stealth
function Invoke-WMIExecution {
    param([string]$BinaryPath)
    
    try {
        # Use WMI to execute process (often bypasses monitoring)
        $processStartup = Get-WmiObject -Class Win32_ProcessStartup
        $processStartup.ShowWindow = 0  # Hidden window
        
        $result = Invoke-WmiMethod -Class Win32_Process -Name Create -ArgumentList $BinaryPath, $null, $processStartup
        
        if ($result.ReturnValue -eq 0) {
            Write-Host "WMI execution successful. PID: $($result.ProcessId)"
            return $result.ProcessId
        }
        else {
            Write-Error "WMI execution failed with code: $($result.ReturnValue)"
            return $null
        }
    }
    catch {
        Write-Error "WMI execution error: $_"
        return $null
    }
}

# Memory-only execution
function Invoke-MemoryExecution {
    param([byte[]]$BinaryData)
    
    try {
        # Load binary into memory and execute (simplified)
        $assembly = [System.Reflection.Assembly]::Load($BinaryData)
        $entryPoint = $assembly.EntryPoint
        
        if ($entryPoint) {
            $entryPoint.Invoke($null, @())
            return $true
        }
    }
    catch {
        # Fallback to native execution methods
        Write-Warning "Memory execution failed, using alternative method"
        return $false
    }
}

# Cleanup function
function Remove-StealthTraces {
    param([string]$FakeName)
    
    try {
        # Remove registry entries
        $appPathKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\$FakeName"
        $uninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$($FakeName.Replace('.exe', ''))"
        
        Remove-Item -Path $appPathKey -Force -ErrorAction SilentlyContinue
        Remove-Item -Path $uninstallKey -Force -ErrorAction SilentlyContinue
        
        # Clear PowerShell history
        Clear-History
        
        # Clear event logs (requires admin)
        if (([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
            wevtutil cl "Windows PowerShell"
            wevtutil cl "Microsoft-Windows-PowerShell/Operational"
        }
        
        Write-Host "Cleanup completed"
    }
    catch {
        Write-Warning "Cleanup partially failed: $_"
    }
}

# Main execution function
function Start-StealthExecution {
    Write-Host "=== Advanced Stealth Execution Framework ===" -ForegroundColor Cyan
    Write-Host "Security Research Tool - Use Responsibly" -ForegroundColor Yellow
    
    # Anti-analysis checks
    $analysisIndicators = Test-AnalysisEnvironment
    if ($analysisIndicators.Count -gt 0) {
        Write-Warning "Analysis environment detected:"
        $analysisIndicators | ForEach-Object { Write-Warning "  - $_" }
        
        $continue = Read-Host "Continue anyway? (y/N)"
        if ($continue -ne 'y' -and $continue -ne 'Y') {
            Write-Host "Execution aborted" -ForegroundColor Red
            return
        }
    }
    
    # Check if target binary exists
    if (-not (Test-Path $TargetBinary)) {
        Write-Error "Target binary not found: $TargetBinary"
        return
    }
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    }
    
    # Generate random name and obfuscate binary
    $fakeName = Get-RandomProcessName
    $obfuscatedPath = Join-Path $OutputDir $fakeName
    
    Write-Host "Generating obfuscated binary: $fakeName" -ForegroundColor Green
    
    if (Invoke-BinaryObfuscation -BinaryPath $TargetBinary -OutputPath $obfuscatedPath) {
        Write-Host "Binary obfuscation successful" -ForegroundColor Green
        
        # Set stealth registry entries
        if (Set-StealthRegistryEntries -FakeName $fakeName -RealPath $obfuscatedPath) {
            Write-Host "Stealth registry entries created" -ForegroundColor Green
        }
        
        # Execute using multiple methods
        Write-Host "Attempting stealth execution..." -ForegroundColor Yellow
        
        # Method 1: WMI execution
        $fullPath = (Resolve-Path $TargetBinary).Path
        Write-Host "Executing: $fullPath" -ForegroundColor Cyan
        $processId = Invoke-WMIExecution -BinaryPath $fullPath

        if ($processId) {
            Write-Host "Stealth execution successful via WMI. PID: $processId" -ForegroundColor Green
            Write-Host "JoyToKey.exe is now running in stealth mode!" -ForegroundColor Green

            # Monitor execution
            Write-Host "Monitoring execution. Press Ctrl+C to stop and cleanup..."
            try {
                while (Get-Process -Id $processId -ErrorAction SilentlyContinue) {
                    Start-Sleep -Seconds 5
                    Write-Host "." -NoNewline -ForegroundColor Green
                }
                Write-Host "`nProcess has terminated." -ForegroundColor Yellow
            }
            catch {
                Write-Host "`nMonitoring interrupted" -ForegroundColor Yellow
            }
        }
        else {
            Write-Warning "WMI execution failed, trying direct execution method"

            # Method 2: Direct stealth execution
            try {
                $startInfo = New-Object System.Diagnostics.ProcessStartInfo
                $startInfo.FileName = $fullPath
                $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Hidden
                $startInfo.CreateNoWindow = $true

                $process = [System.Diagnostics.Process]::Start($startInfo)

                if ($process) {
                    Write-Host "Direct stealth execution successful. PID: $($process.Id)" -ForegroundColor Green
                    Write-Host "JoyToKey.exe is now running in stealth mode!" -ForegroundColor Green
                    $processId = $process.Id
                }
            }
            catch {
                Write-Error "All execution methods failed: $_"
            }
        }
        
        # Cleanup
        Write-Host "Cleaning up traces..." -ForegroundColor Yellow
        Remove-StealthTraces -FakeName $fakeName
        Remove-Item -Path $obfuscatedPath -Force -ErrorAction SilentlyContinue
    }
    else {
        Write-Error "Binary obfuscation failed"
    }
}

# Execute main function
Start-StealthExecution
