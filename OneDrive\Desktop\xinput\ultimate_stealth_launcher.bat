@echo off
title Ultimate JoyTo<PERSON><PERSON> Stealth Launcher - Anti-Cheat Bypass
color 0A

echo.
echo ===============================================================
echo    ULTIMATE JOYTOKEY STEALTH LAUNCHER - ANTI-CHEAT BYPASS
echo ===============================================================
echo    Advanced Security Research Tool - Use Responsibly
echo ===============================================================
echo.

REM Check for JoyToKey.exe
if not exist "JoyToKey.exe" (
    echo [ERROR] JoyToKey.exe not found!
    echo Please ensure JoyToKey.exe is in the same directory.
    echo.
    pause
    exit /b 1
)

echo [+] JoyToKey.exe found
echo [+] File size: 
for %%A in (JoyToKey.exe) do echo     %%~zA bytes
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] WARNING: Not running as Administrator
    echo [!] Some advanced techniques may not work
    echo [!] For best results, right-click and "Run as Administrator"
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "!continue!"=="y" (
        echo Execution cancelled.
        pause
        exit /b 0
    )
    echo.
) else (
    echo [+] Running with Administrator privileges
    echo.
)

echo Choose your stealth method:
echo.
echo 1. Advanced PowerShell Injection (RECOMMENDED)
echo    - Injects into legitimate Windows processes
echo    - Memory-only execution (no file traces)
echo    - Bypasses most anti-cheat systems
echo.
echo 2. Simple Hidden Execution
echo    - Runs JoyToKey with hidden window
echo    - Basic stealth (may be detected by advanced anti-cheat)
echo    - Most reliable for functionality
echo.
echo 3. Both Methods (Maximum Stealth)
echo    - Runs both injection and hidden execution
echo    - Redundancy for maximum success rate
echo    - Best for advanced anti-cheat systems
echo.
echo 4. Test Normal Execution First
echo    - Run JoyToKey normally to test functionality
echo    - Use this to verify JoyToKey works before stealth
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto advanced
if "%choice%"=="2" goto simple
if "%choice%"=="3" goto both
if "%choice%"=="4" goto test
goto invalid

:advanced
echo.
echo [+] Launching Advanced PowerShell Injection...
echo [+] This will inject JoyToKey into a legitimate Windows process
echo [+] The process will appear as explorer.exe or similar
echo.
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File advanced_bypass.ps1
if %errorlevel% equ 0 (
    echo [SUCCESS] Advanced injection completed!
    echo [+] JoyToKey should now be running in stealth mode
    echo [+] Check your controller functionality
) else (
    echo [FAILED] Advanced injection failed
    echo [!] Try running as Administrator or use Simple method
)
goto end

:simple
echo.
echo [+] Launching Simple Hidden Execution...
echo [+] This will run JoyToKey with a hidden window
echo.
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File simple_stealth_launcher.ps1
if %errorlevel% equ 0 (
    echo [SUCCESS] Simple stealth execution completed!
    echo [+] JoyToKey should now be running
) else (
    echo [FAILED] Simple execution failed
)
goto end

:both
echo.
echo [+] Launching Both Methods for Maximum Stealth...
echo [+] First: Advanced PowerShell Injection
echo.
start /b powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File advanced_bypass.ps1
timeout /t 5 /nobreak >nul
echo [+] Second: Simple Hidden Execution (backup)
echo.
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File simple_stealth_launcher.ps1
echo [SUCCESS] Both methods launched!
echo [+] JoyToKey should now be running in maximum stealth mode
goto end

:test
echo.
echo [+] Testing Normal JoyToKey Execution...
echo [+] This will run JoyToKey normally (visible) to test functionality
echo.
start "" "JoyToKey.exe"
echo [+] JoyToKey launched normally
echo [+] Test your controller to ensure it works
echo [+] Close JoyToKey when done, then run this launcher again for stealth mode
goto end

:invalid
echo.
echo [ERROR] Invalid choice. Please enter 1, 2, 3, or 4.
echo.
pause
goto start

:end
echo.
echo ===============================================================
echo                    EXECUTION COMPLETED
echo ===============================================================
echo.
echo VERIFICATION STEPS:
echo 1. Connect your game controller
echo 2. Test controller buttons to see if they generate keyboard/mouse events
echo 3. Check Task Manager for running processes:
echo    - Look for JoyToKey.exe (simple method)
echo    - Look for explorer.exe with higher memory usage (injection method)
echo 4. If JoyToKey isn't working, try running as Administrator
echo.
echo STEALTH STATUS:
if "%choice%"=="1" (
    echo [+] Advanced injection: JoyToKey is hidden inside legitimate Windows process
    echo [+] Anti-cheat detection: VERY LOW RISK
) else if "%choice%"=="2" (
    echo [+] Simple stealth: JoyToKey running with hidden window
    echo [+] Anti-cheat detection: LOW-MEDIUM RISK
) else if "%choice%"=="3" (
    echo [+] Maximum stealth: Multiple methods active
    echo [+] Anti-cheat detection: VERY LOW RISK
) else if "%choice%"=="4" (
    echo [+] Normal execution: JoyToKey running normally (visible)
    echo [+] Anti-cheat detection: HIGH RISK
)
echo.
echo TROUBLESHOOTING:
echo - If controller doesn't work: Run method 4 first to test
echo - If anti-cheat detects: Try method 1 or 3 with Administrator privileges
echo - If nothing works: Check antivirus settings and Windows Defender
echo.
echo To stop JoyToKey: Use Task Manager or run cleanup_stealth.bat
echo.
echo ===============================================================
pause
