# JoyTo<PERSON>ey Stealth Execution Verification Script

Write-Host "=== JoyToKey Stealth Execution Verification ===" -ForegroundColor Cyan
Write-Host "This script will verify that JoyTo<PERSON><PERSON> is running after stealth execution" -ForegroundColor Yellow
Write-Host ""

function Test-JoyToKeyRunning {
    Write-Host "[+] Checking for JoyToKey processes..." -ForegroundColor Green
    
    # Check for direct JoyToKey processes
    $joyProcesses = Get-Process | Where-Object { 
        $_.ProcessName -like "*joy*" -or 
        $_.MainWindowTitle -like "*JoyToKey*" -or
        $_.Path -like "*JoyToKey*"
    }
    
    if ($joyProcesses) {
        Write-Host "[+] Found JoyToKey processes:" -ForegroundColor Green
        $joyProcesses | ForEach-Object {
            Write-Host "    - PID: $($_.Id), Name: $($_.ProcessName), Path: $($_.Path)" -ForegroundColor White
        }
        return $true
    }
    
    # Check for processes that might be hollowed with <PERSON>To<PERSON>ey
    Write-Host "[+] Checking for potential process hollowing targets..." -ForegroundColor Yellow
    
    $suspiciousProcesses = Get-Process | Where-Object { 
        $_.ProcessName -eq "notepad" -or 
        $_.ProcessName -eq "calc" -or 
        $_.ProcessName -like "svchost*" 
    } | Where-Object {
        # Check if these processes have unusual characteristics
        try {
            $_.Modules | Out-Null
            return $false  # If we can enumerate modules, it's probably legitimate
        } catch {
            return $true   # If we can't enumerate modules, it might be hollowed
        }
    }
    
    if ($suspiciousProcesses) {
        Write-Host "[+] Found potentially hollowed processes:" -ForegroundColor Yellow
        $suspiciousProcesses | ForEach-Object {
            Write-Host "    - PID: $($_.Id), Name: $($_.ProcessName)" -ForegroundColor White
        }
    }
    
    return $false
}

function Test-JoyToKeyFunctionality {
    Write-Host "[+] Testing JoyToKey functionality..." -ForegroundColor Green
    
    # Check if JoyToKey configuration files exist
    $configPaths = @(
        "JoyToKey.cfg",
        "JoyToKey.ini",
        "$env:APPDATA\JoyToKey\*"
    )
    
    $configFound = $false
    foreach ($path in $configPaths) {
        if (Test-Path $path) {
            Write-Host "[+] Found JoyToKey config: $path" -ForegroundColor Green
            $configFound = $true
        }
    }
    
    if (-not $configFound) {
        Write-Host "[!] No JoyToKey configuration files found" -ForegroundColor Yellow
        Write-Host "    This might indicate JoyToKey hasn't been configured yet" -ForegroundColor Yellow
    }
    
    # Check for JoyToKey registry entries
    try {
        $regKey = Get-ItemProperty -Path "HKCU:\Software\JoyToKey" -ErrorAction SilentlyContinue
        if ($regKey) {
            Write-Host "[+] Found JoyToKey registry entries" -ForegroundColor Green
        }
    } catch {
        Write-Host "[!] No JoyToKey registry entries found" -ForegroundColor Yellow
    }
}

function Show-ProcessDetails {
    Write-Host "[+] Current process overview:" -ForegroundColor Cyan
    
    # Show all processes with their details
    Get-Process | Where-Object { 
        $_.ProcessName -like "*joy*" -or 
        $_.ProcessName -eq "notepad" -or 
        $_.ProcessName -eq "calc" -or
        $_.ProcessName -like "*stealth*"
    } | Select-Object ProcessName, Id, @{Name="Memory(MB)"; Expression={[math]::Round($_.WorkingSet/1MB,2)}}, Path | 
    Format-Table -AutoSize
}

# Main execution
Write-Host "Starting verification process..." -ForegroundColor Cyan
Write-Host ""

# Test 1: Check if JoyToKey is running
$isRunning = Test-JoyToKeyRunning

if ($isRunning) {
    Write-Host "[SUCCESS] JoyToKey appears to be running!" -ForegroundColor Green
} else {
    Write-Host "[WARNING] JoyToKey processes not clearly detected" -ForegroundColor Yellow
    Write-Host "This could mean:" -ForegroundColor Yellow
    Write-Host "  - JoyToKey is running in stealth mode (good!)" -ForegroundColor Yellow
    Write-Host "  - JoyToKey failed to start" -ForegroundColor Yellow
    Write-Host "  - JoyToKey is running under a different process name" -ForegroundColor Yellow
}

Write-Host ""

# Test 2: Check JoyToKey functionality
Test-JoyToKeyFunctionality

Write-Host ""

# Test 3: Show process details
Show-ProcessDetails

Write-Host ""
Write-Host "=== Verification Complete ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "To manually verify JoyToKey is working:" -ForegroundColor White
Write-Host "1. Connect a game controller" -ForegroundColor White
Write-Host "2. Try pressing controller buttons" -ForegroundColor White
Write-Host "3. Check if keyboard/mouse inputs are generated" -ForegroundColor White
Write-Host "4. Look for JoyToKey system tray icon (if not in stealth mode)" -ForegroundColor White
Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
