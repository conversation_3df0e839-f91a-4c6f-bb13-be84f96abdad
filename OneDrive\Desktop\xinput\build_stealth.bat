@echo off
echo ===================================================
echo Advanced Stealth Framework Builder
echo Security Research Tool - Use Responsibly
echo ===================================================

REM Check for Visual Studio Build Tools
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo [!] Visual Studio Build Tools not found in PATH
    echo [+] Attempting to locate VS Build Tools...
    
    REM Try common VS installation paths
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo [!] Could not locate Visual Studio Build Tools
        echo [!] Please install Visual Studio Build Tools or Visual Studio Community
        echo [!] Download from: https://visualstudio.microsoft.com/downloads/
        pause
        exit /b 1
    )
)

echo [+] Compiling stealth loader...

REM Compile with advanced optimization and anti-analysis features
cl /O2 /GL /DNDEBUG /MT ^
   /D_CRT_SECURE_NO_WARNINGS ^
   /D_WIN32_WINNT=0x0601 ^
   stealth_loader.cpp ^
   /link /LTCG /SUBSYSTEM:CONSOLE ^
   kernel32.lib user32.lib advapi32.lib ^
   ntdll.lib psapi.lib ^
   /OUT:stealth_loader.exe

if %errorlevel% neq 0 (
    echo [!] Compilation failed
    pause
    exit /b 1
)

echo [+] Compilation successful!

REM Clean up intermediate files
del *.obj >nul 2>nul

REM Create directory structure
if not exist "stealth_output" mkdir stealth_output
if not exist "backup" mkdir backup

echo [+] Creating backup of original binary...
copy JoyToKey.exe backup\JoyToKey_original.exe >nul

echo [+] Setting up stealth environment...

REM Create a simple launcher script
echo @echo off > launch_stealth.bat
echo echo [+] Launching Advanced Stealth Framework >> launch_stealth.bat
echo echo [!] Ensure you have administrative privileges for full functionality >> launch_stealth.bat
echo echo. >> launch_stealth.bat
echo REM Check for admin privileges >> launch_stealth.bat
echo net session ^>nul 2^>^&1 >> launch_stealth.bat
echo if %%errorlevel%% neq 0 ( >> launch_stealth.bat
echo     echo [!] Warning: Not running as administrator >> launch_stealth.bat
echo     echo [!] Some features may not work properly >> launch_stealth.bat
echo     echo. >> launch_stealth.bat
echo ^) >> launch_stealth.bat
echo. >> launch_stealth.bat
echo echo Choose execution method: >> launch_stealth.bat
echo echo 1. Native C++ Stealth Loader >> launch_stealth.bat
echo echo 2. PowerShell Advanced Stealth >> launch_stealth.bat
echo echo 3. Both methods >> launch_stealth.bat
echo echo. >> launch_stealth.bat
echo set /p choice="Enter choice (1-3): " >> launch_stealth.bat
echo. >> launch_stealth.bat
echo if "%%choice%%"=="1" goto native >> launch_stealth.bat
echo if "%%choice%%"=="2" goto powershell >> launch_stealth.bat
echo if "%%choice%%"=="3" goto both >> launch_stealth.bat
echo goto end >> launch_stealth.bat
echo. >> launch_stealth.bat
echo :native >> launch_stealth.bat
echo echo [+] Launching Native C++ Stealth Loader... >> launch_stealth.bat
echo stealth_loader.exe >> launch_stealth.bat
echo goto end >> launch_stealth.bat
echo. >> launch_stealth.bat
echo :powershell >> launch_stealth.bat
echo echo [+] Launching PowerShell Advanced Stealth... >> launch_stealth.bat
echo powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1 >> launch_stealth.bat
echo goto end >> launch_stealth.bat
echo. >> launch_stealth.bat
echo :both >> launch_stealth.bat
echo echo [+] Launching both methods... >> launch_stealth.bat
echo start /b stealth_loader.exe >> launch_stealth.bat
echo timeout /t 2 /nobreak ^>nul >> launch_stealth.bat
echo powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1 >> launch_stealth.bat
echo goto end >> launch_stealth.bat
echo. >> launch_stealth.bat
echo :end >> launch_stealth.bat
echo echo [+] Execution completed >> launch_stealth.bat
echo pause >> launch_stealth.bat

REM Create cleanup script
echo @echo off > cleanup_stealth.bat
echo echo [+] Cleaning up stealth traces... >> cleanup_stealth.bat
echo. >> cleanup_stealth.bat
echo REM Kill any running processes >> cleanup_stealth.bat
echo taskkill /f /im stealth_loader.exe ^>nul 2^>^&1 >> cleanup_stealth.bat
echo taskkill /f /im JoyToKey.exe ^>nul 2^>^&1 >> cleanup_stealth.bat
echo. >> cleanup_stealth.bat
echo REM Clean up files >> cleanup_stealth.bat
echo if exist "stealth_output" rmdir /s /q stealth_output >> cleanup_stealth.bat
echo. >> cleanup_stealth.bat
echo REM Clean up registry (requires admin) >> cleanup_stealth.bat
echo net session ^>nul 2^>^&1 >> cleanup_stealth.bat
echo if %%errorlevel%% equ 0 ( >> cleanup_stealth.bat
echo     echo [+] Cleaning registry entries... >> cleanup_stealth.bat
echo     reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths" /f ^>nul 2^>^&1 >> cleanup_stealth.bat
echo ^) else ( >> cleanup_stealth.bat
echo     echo [!] Admin privileges required for full cleanup >> cleanup_stealth.bat
echo ^) >> cleanup_stealth.bat
echo. >> cleanup_stealth.bat
echo echo [+] Cleanup completed >> cleanup_stealth.bat
echo pause >> cleanup_stealth.bat

REM Create configuration file
echo # Advanced Stealth Configuration > stealth_config.txt
echo # Modify these settings for different evasion techniques >> stealth_config.txt
echo. >> stealth_config.txt
echo [GENERAL] >> stealth_config.txt
echo TARGET_BINARY=JoyToKey.exe >> stealth_config.txt
echo OUTPUT_DIR=stealth_output >> stealth_config.txt
echo BACKUP_DIR=backup >> stealth_config.txt
echo. >> stealth_config.txt
echo [EVASION] >> stealth_config.txt
echo ENABLE_ANTI_DEBUG=true >> stealth_config.txt
echo ENABLE_ANTI_VM=true >> stealth_config.txt
echo ENABLE_PROCESS_HOLLOWING=true >> stealth_config.txt
echo ENABLE_REGISTRY_SPOOFING=true >> stealth_config.txt
echo ENABLE_MEMORY_ENCRYPTION=true >> stealth_config.txt
echo. >> stealth_config.txt
echo [ADVANCED] >> stealth_config.txt
echo ENCRYPTION_KEY=DynamicKey2024 >> stealth_config.txt
echo FAKE_PROCESS_NAMES=svchost,dwm,explorer,winlogon >> stealth_config.txt
echo DELAY_EXECUTION_MS=5000 >> stealth_config.txt

echo.
echo ===================================================
echo Build completed successfully!
echo ===================================================
echo.
echo Files created:
echo   - stealth_loader.exe      (Native C++ loader)
echo   - advanced_stealth.ps1    (PowerShell framework)
echo   - launch_stealth.bat      (Main launcher)
echo   - cleanup_stealth.bat     (Cleanup utility)
echo   - stealth_config.txt      (Configuration file)
echo.
echo Usage:
echo   1. Run 'launch_stealth.bat' to start
echo   2. Choose your preferred execution method
echo   3. Run 'cleanup_stealth.bat' when finished
echo.
echo [!] IMPORTANT SECURITY NOTES:
echo   - This is for legitimate security research only
echo   - Requires administrative privileges for full functionality
echo   - Some antivirus software may flag these tools
echo   - Use only in controlled environments
echo   - Always clean up after testing
echo.
echo ===================================================
pause
