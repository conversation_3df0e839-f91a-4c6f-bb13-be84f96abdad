# Advanced Stealth Execution Framework

## ⚠️ IMPORTANT DISCLAIMER
This framework is designed for **legitimate security research and testing purposes only**. Use responsibly and only in controlled environments where you have explicit permission.

## Overview
This advanced stealth framework provides multiple sophisticated techniques to execute JoyToKey.exe while evading detection by anti-cheat systems and security monitoring tools.

## Features

### 🛡️ Anti-Analysis Techniques
- **Debugger Detection**: Multiple methods to detect debugging environments
- **Virtual Machine Detection**: Identifies VM artifacts and sandbox environments  
- **Hook Detection**: Detects API hooks commonly used by monitoring tools
- **Environment Fingerprinting**: Analyzes system characteristics for analysis tools

### 🔒 Binary Obfuscation
- **PE Header Modification**: Changes timestamps, section names, and entry points
- **Entropy Addition**: Adds random data sections to alter file signatures
- **Dynamic Encryption**: XOR encryption with time-based keys
- **Signature Evasion**: Modifies static signatures that trigger detection

### 💉 Advanced Injection Techniques
- **Process Hollowing**: Executes payload inside legitimate processes
- **Manual DLL Mapping**: Bypasses standard DLL loading mechanisms
- **Reflective DLL Injection**: Loads DLLs directly from memory
- **WMI Execution**: Uses Windows Management Instrumentation for stealth

### 🗃️ Registry Manipulation
- **Fake Process Entries**: Creates legitimate-looking registry entries
- **Dynamic Spoofing**: Modifies registry values at runtime
- **Cleanup Mechanisms**: Removes traces after execution

### 🧠 Memory Protection
- **Runtime Encryption**: Encrypts process memory during execution
- **Anti-Dumping**: Prevents memory dumping by analysis tools
- **Code Obfuscation**: Obfuscates critical code sections

## Components

### 1. Native C++ Stealth Loader (`stealth_loader.cpp`)
Advanced native implementation with:
- Process hollowing capabilities
- Anti-analysis detection
- Memory encryption
- Registry manipulation

### 2. PowerShell Advanced Framework (`advanced_stealth.ps1`)
PowerShell-based solution featuring:
- WMI execution methods
- Binary obfuscation
- Environment detection
- Automated cleanup

### 3. Stealth Injector (`stealth_injector.cpp`)
Specialized injection tool with:
- Multiple injection methods
- Manual DLL mapping
- Reflective loading
- Target process discovery

## Quick Start

### Prerequisites
- Windows 10/11 (x64)
- Visual Studio Build Tools or Visual Studio Community
- Administrative privileges (recommended)
- PowerShell execution policy set to Bypass

### Build Instructions

1. **Compile the Framework**
   ```batch
   build_stealth.bat
   ```

2. **Launch the Framework**
   ```batch
   launch_stealth.bat
   ```

3. **Choose Execution Method**
   - Option 1: Native C++ Stealth Loader
   - Option 2: PowerShell Advanced Stealth
   - Option 3: Both methods simultaneously

### Manual Usage

#### Native C++ Loader
```batch
stealth_loader.exe
```

#### PowerShell Framework
```powershell
powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1
```

#### Stealth Injector
```batch
# DLL injection into specific process
stealth_injector.exe JoyToKey.exe 1234

# Process hollowing
stealth_injector.exe JoyToKey.exe hollow C:\Windows\System32\notepad.exe
```

## Configuration

### Stealth Configuration (`stealth_config.txt`)
```ini
[GENERAL]
TARGET_BINARY=JoyToKey.exe
OUTPUT_DIR=stealth_output
BACKUP_DIR=backup

[EVASION]
ENABLE_ANTI_DEBUG=true
ENABLE_ANTI_VM=true
ENABLE_PROCESS_HOLLOWING=true
ENABLE_REGISTRY_SPOOFING=true
ENABLE_MEMORY_ENCRYPTION=true

[ADVANCED]
ENCRYPTION_KEY=DynamicKey2024
FAKE_PROCESS_NAMES=svchost,dwm,explorer,winlogon
DELAY_EXECUTION_MS=5000
```

## Advanced Techniques Explained

### Process Hollowing
1. Creates a suspended legitimate process (e.g., notepad.exe)
2. Unmaps the original process image from memory
3. Allocates new memory space for the payload
4. Writes the target binary to the allocated space
5. Updates the process entry point
6. Resumes execution with the new payload

### Manual DLL Mapping
1. Parses PE headers of the target DLL
2. Allocates memory in the target process
3. Manually maps sections to memory
4. Resolves imports and relocations
5. Executes the DLL entry point

### Registry Spoofing
1. Creates fake application registry entries
2. Mimics legitimate Windows components
3. Adds uninstall entries for authenticity
4. Uses dynamic cleanup mechanisms

## Detection Evasion Strategies

### Static Analysis Evasion
- **File Signature Modification**: Changes PE characteristics
- **Entropy Manipulation**: Adds random data to alter file structure
- **Timestamp Spoofing**: Modifies compilation timestamps
- **Section Renaming**: Changes section names to common ones

### Dynamic Analysis Evasion
- **Delayed Execution**: Waits before executing payload
- **Environment Checks**: Validates execution environment
- **API Obfuscation**: Uses indirect API calls
- **Memory Encryption**: Encrypts sensitive data in memory

### Behavioral Evasion
- **Legitimate Process Mimicking**: Appears as system processes
- **WMI Execution**: Uses Windows management interfaces
- **Registry Integration**: Creates authentic-looking entries
- **Cleanup Automation**: Removes traces automatically

## Cleanup and Safety

### Automatic Cleanup
The framework includes automatic cleanup mechanisms:
- Removes temporary files
- Clears registry entries
- Terminates spawned processes
- Clears PowerShell history

### Manual Cleanup
```batch
cleanup_stealth.bat
```

### Safety Measures
- Anti-analysis detection prevents execution in monitored environments
- Automatic cleanup reduces forensic traces
- Administrative privilege checks ensure proper functionality
- Configuration validation prevents misuse

## Troubleshooting

### Common Issues

1. **Compilation Errors**
   - Ensure Visual Studio Build Tools are installed
   - Check PATH environment variable
   - Run as administrator

2. **Execution Failures**
   - Verify administrative privileges
   - Check antivirus exclusions
   - Validate target binary path

3. **Detection Issues**
   - Update evasion techniques
   - Modify configuration parameters
   - Use different execution methods

### Debug Mode
Enable debug output by modifying the configuration:
```ini
[DEBUG]
ENABLE_LOGGING=true
LOG_LEVEL=verbose
LOG_FILE=stealth_debug.log
```

## Legal and Ethical Considerations

### Legitimate Use Cases
- Security research and testing
- Red team exercises
- Penetration testing
- Educational purposes
- Malware analysis research

### Prohibited Uses
- Cheating in online games
- Bypassing legitimate security measures
- Unauthorized system access
- Malicious software distribution

## Support and Updates

This framework is provided for educational and research purposes. Users are responsible for ensuring compliance with applicable laws and regulations.

### Version History
- v1.0: Initial release with basic stealth capabilities
- v1.1: Added advanced injection techniques
- v1.2: Enhanced anti-analysis detection
- v1.3: Improved registry manipulation

---

**Remember**: Always use this framework responsibly and only in environments where you have explicit permission to conduct security testing.
