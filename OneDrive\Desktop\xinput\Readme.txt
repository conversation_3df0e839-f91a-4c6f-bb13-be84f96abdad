﻿======================================================================

                        JoyToKey Version 7.1.1

           Copyright(C) 1999-2025, JTK and Free Colors, Ltd.

======================================================================


1. OVERVIEW
-----------
This software enables you to control various windows applications (e.g.
a web browser, games without controller support, Photoshop shortcuts, 
Microsoft office, and even Windows itself) by using your favorite joystick.  
Whenever you press joystick buttons and sticks, <PERSON>ToKey will convert the 
input into keyboard strokes and mouse movements so that the target 
application can be controlled as you pre-configured!

NOTE: Due to enhanced security control in recent Windows versions, you 
may not be able to control some applications.  If you encounter such
problems, try to launch JoyToKey.exe by opening right-click popup menu
and selecting "Run As Administrator".


2. KEY FEATURES
---------------
* Create multiple configuration files
  You can create multiple configuration files and switch them at any time.
  For example, create one config for a game, and create another one for
  web browsing.  JoyToKey also supports the automatic switching of an
  active configuration file when the target application changes.

* Assign multiple keys for each button
  For example, you can assign a combination of Alt+F4 for one joystick
  button.

* Mouse emulation
  You can also emulate mouse cursor movements, clicks and wheel rotation.

* Automatic repeat of button input or toggle input

* Virtual joystick setting
  Even if you have only one joystick, you can configure button mapping for
  multiple virtual joysticks (e.g. one joystick for default key mapping,
  another virtual joystick for registering function keys or shortcuts, and
  yet another virtual joystick for mouse control).
  You can switch to those virtual joystick mappings temporarily by pressing
  special buttons.


3. HOW TO INSTALL
-----------------
If you downloaded the installer file (.exe), simply execute the installer.

If you downloaded the zip archive file (.zip), simply unzip the archive file 
into some folder, and launch JoyToKey.exe from there.  To uninstall this 
software, you only need to remove that folder.
(This software doesn't use a registry)

Please make sure that the installed folder is not write 
protected as it needs to save configuration files in the same folder.

System requirement:
  * OS: Windows 10, 8.1, 8, 7, Vista
  * DirectX 8.0 or above


4. USAGE
--------
* Press "Create" button and create a new configuration file

* In the right panel, choose a button from the list and double click 
(or press enter key) to open a button assignment window.

* For example, if you want to control Internet Explorer with joysticks,
  you could configure as follows:
    "Back" : Alt + Left
    "Close Window" : Alt + F + C
    "Move To Menu" : Alt + F
  And you may also want to add mouse cursor movement, wheel etc.

* If you are a new user and you have some trouble configuring JoyToKey,
  please follow the instructions below.

  i) I recommend you to try to use it with a simple application like
     "Notepad.exe".

  ii) Please configure a few joystick buttons mapping to "a", "b" keys.
     And then open a Notepad and press those joystick buttons.
     Character "a" and "b" should be entered in the Notepad.
     (Be sure to keep the JoyToKey program running, iconized in the task
      tray at the bottom of the desktop.)

  iii) If it doesn't work, go to menu "Configuration -> Calibrate joystick
     property" (or go to "control panel" from Windows Start menu), and
     check game controller properties.  Please make sure your joystick
     is recognized and sticks and buttons are calibrated properly.

* For general questions, please refer to FAQ page at 
  https://joytokey.net/en/faq


5. TIPS
-------
* SHIFT key function (switch to other joystick mapping temporarily)
  At first, this feature (aka virtual joystick) may sound difficult to
  understand, but it is very useful as it enables you to configure many
  key mapping, more than the number of buttons you actually have!

  For example, you may have only one joystick with only 6 buttons.
  Then, you can only assign 6 different key mapping by default.  However,
  by assigning one button to switch to a virtual joystick-2, you can assign
  5+5=10 different key mappings (Note: 1 button is used to switch between
  two mappings).  You could further assign another button to switch to a
  virtual joystick-3, which will result in total 5+4+4=13 different key
  mappings.

  Normally, you define the most frequently used key mappings for a physical
  joystick-1.  Then, you could define less used key mappings for virtual
  joystick-2, 3, 4, ...

* Configuration file (*.cfg) is simply a text file. So, If you want to
  rename the configuration, copy the configuration, backup the files etc.,
  terminate JoyToKey and simply rename or copy the file (*.cfg) in Explorer.


6. SPECIAL THANKS
-----------------

* JoyToKey can be used for free to help people with disabilities, and there 
  are many people and organizations who provided useful feedbacks and TIPs 
  to improve and utilize JoyToKey for that purpose.

  For example, you can find more details from the following sites.
  https://dekimouse.org/wp/
  https://www.oneswitch.org.uk/

* Special thanks to Peter (Silent_ip), who created a new high-resolution icon
  for JoyToKey!


7. ABOUT JOYTOKEY
-----------------

Firstly, thank you for downloading and using JoyToKey.

JoyToKey is a shareware, but you can freely try it until you like it without
much limitation in functionality.  After trial, if you find it useful, you
can purchase a license key from the website.

https://joytokey.net/en/purchase

(or simply go to JoyToKey menu "License Key" -> "Purchase License Key")

For commercial usage, licence needs to be purchased for each user.
For personal usage, you can install and use JoyToKey on multiple PCs
with one license key.

Your contribution is greatly appreciated and will be used to continue the
development of JoyToKey.

DISCLAIMER:
This product comes with no warranty. Use it at your own risk. The author
takes no responsibilities of any sort related to the use of this product.

JoyToKey is by no means perfect and may not work for certain type of 
applications.

If you have some comments, suggestions or bug reports, please feel free 
to send an email to the author.  Due to the number of emails, you may not
receive a response, but I will try my best to read all the messages and
reflect them in the future release.

Thanks again for using JoyToKey, and I hope it'll help you to improve
your PC life.

Best Regards,

JTK

Homepage: https://joytokey.net/
E-Mail: <EMAIL>
