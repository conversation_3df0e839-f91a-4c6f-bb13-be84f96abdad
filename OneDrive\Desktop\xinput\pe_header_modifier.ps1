# PE Header Modifier for True Process Name Spoofing
# This modifies the internal PE headers to change the process name completely

param(
    [string]$InputFile = "JoyToKey.exe",
    [string]$OutputFile = "notepad_stealth.exe",
    [string]$NewProcessName = "notepad"
)

Write-Host "=== PE Header Modifier for Process Name Spoofing ===" -ForegroundColor Cyan
Write-Host "Modifying internal PE headers for true stealth" -ForegroundColor Yellow
Write-Host "=============================================" -ForegroundColor Cyan

function Modify-PEHeaders {
    param(
        [string]$SourceFile,
        [string]$DestFile,
        [string]$NewName
    )
    
    try {
        # Read the original file
        $fileBytes = [System.IO.File]::ReadAllBytes($SourceFile)
        Write-Host "[+] Loaded original file: $SourceFile ($($fileBytes.Length) bytes)" -ForegroundColor Green
        
        # Parse DOS header
        $dosSignature = [System.BitConverter]::ToUInt16($fileBytes, 0)
        if ($dosSignature -ne 0x5A4D) {
            Write-Host "[-] Invalid DOS signature" -ForegroundColor Red
            return $false
        }
        
        # Get PE header offset
        $peOffset = [System.BitConverter]::ToInt32($fileBytes, 60)
        Write-Host "[+] PE header offset: 0x$($peOffset.ToString('X8'))" -ForegroundColor Green
        
        # Verify PE signature
        $peSignature = [System.BitConverter]::ToUInt32($fileBytes, $peOffset)
        if ($peSignature -ne 0x00004550) {
            Write-Host "[-] Invalid PE signature" -ForegroundColor Red
            return $false
        }
        
        # Modify version info and strings
        Write-Host "[+] Searching for version info and strings..." -ForegroundColor Yellow
        
        # Common strings to replace in JoyToKey
        $stringsToReplace = @{
            "JoyToKey" = "Notepad"
            "Joy2Key" = "Notepad"
            "JoyToKey.exe" = "notepad.exe"
            "Keyboard/Mouse Emulator" = "Text Editor"
            "Game Controller" = "Text Document"
            "Controller" = "Document"
            "Emulator" = "Editor"
        }
        
        $replacements = 0
        foreach ($oldString in $stringsToReplace.Keys) {
            $newString = $stringsToReplace[$oldString]
            $oldBytes = [System.Text.Encoding]::ASCII.GetBytes($oldString)
            $newBytes = [System.Text.Encoding]::ASCII.GetBytes($newString)
            
            # Pad new string to same length as old string
            if ($newBytes.Length -lt $oldBytes.Length) {
                $padding = New-Object byte[] ($oldBytes.Length - $newBytes.Length)
                $newBytes = $newBytes + $padding
            } elseif ($newBytes.Length -gt $oldBytes.Length) {
                $newBytes = $newBytes[0..($oldBytes.Length-1)]
            }
            
            # Search and replace in the file
            for ($i = 0; $i -le ($fileBytes.Length - $oldBytes.Length); $i++) {
                $match = $true
                for ($j = 0; $j -lt $oldBytes.Length; $j++) {
                    if ($fileBytes[$i + $j] -ne $oldBytes[$j]) {
                        $match = $false
                        break
                    }
                }
                
                if ($match) {
                    Write-Host "[+] Replacing '$oldString' at offset 0x$($i.ToString('X8'))" -ForegroundColor Green
                    for ($j = 0; $j -lt $newBytes.Length; $j++) {
                        $fileBytes[$i + $j] = $newBytes[$j]
                    }
                    $replacements++
                    $i += $oldBytes.Length - 1  # Skip past this replacement
                }
            }
        }
        
        Write-Host "[+] Made $replacements string replacements" -ForegroundColor Green
        
        # Modify PE timestamp to avoid signature detection
        $timestamp = [System.DateTimeOffset]::Now.ToUnixTimeSeconds()
        $timestampBytes = [System.BitConverter]::GetBytes([uint32]$timestamp)
        for ($i = 0; $i -lt 4; $i++) {
            $fileBytes[$peOffset + 8 + $i] = $timestampBytes[$i]
        }
        Write-Host "[+] Modified PE timestamp" -ForegroundColor Green
        
        # Modify checksum (set to 0 to disable verification)
        $checksumOffset = $peOffset + 24 + 64  # PE + COFF + OptionalHeader.CheckSum
        for ($i = 0; $i -lt 4; $i++) {
            $fileBytes[$checksumOffset + $i] = 0
        }
        Write-Host "[+] Disabled PE checksum verification" -ForegroundColor Green
        
        # Write the modified file
        [System.IO.File]::WriteAllBytes($DestFile, $fileBytes)
        Write-Host "[+] Modified file saved as: $DestFile" -ForegroundColor Green
        
        return $true
        
    } catch {
        Write-Host "[-] Error modifying PE headers: $_" -ForegroundColor Red
        return $false
    }
}

# Check if input file exists
if (-not (Test-Path $InputFile)) {
    Write-Host "[ERROR] Input file not found: $InputFile" -ForegroundColor Red
    exit 1
}

# Modify the PE headers
Write-Host "[+] Starting PE header modification..." -ForegroundColor Yellow
$success = Modify-PEHeaders -SourceFile $InputFile -DestFile $OutputFile -NewName $NewProcessName

if ($success) {
    Write-Host ""
    Write-Host "=== PE MODIFICATION SUCCESSFUL ===" -ForegroundColor Green
    Write-Host "Modified file created: $OutputFile" -ForegroundColor Green
    Write-Host ""
    Write-Host "Changes made:" -ForegroundColor Cyan
    Write-Host "✓ All 'JoyToKey' strings replaced with 'Notepad'" -ForegroundColor White
    Write-Host "✓ PE timestamp modified to avoid detection" -ForegroundColor White
    Write-Host "✓ PE checksum disabled" -ForegroundColor White
    Write-Host "✓ Process should appear as 'notepad' in Task Manager" -ForegroundColor White
    Write-Host ""
    Write-Host "Next step: Run the modified executable" -ForegroundColor Yellow
    Write-Host "Command: .\$OutputFile" -ForegroundColor Gray
    
} else {
    Write-Host ""
    Write-Host "=== PE MODIFICATION FAILED ===" -ForegroundColor Red
    Write-Host "Could not modify PE headers" -ForegroundColor Red
}
