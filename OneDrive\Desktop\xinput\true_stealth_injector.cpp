#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <iostream>
#include <vector>
#include <string>
#include <fstream>

#pragma comment(lib, "psapi.lib")

class TrueStealth {
private:
    std::vector<BYTE> joyToKeyData;
    HANDLE targetProcess;
    DWORD targetPID;
    
    // Find a legitimate Windows process to inject into
    DWORD FindLegitimateProcess() {
        std::vector<std::string> legitimateTargets = {
            "explorer.exe", "dwm.exe", "winlogon.exe", "csrss.exe"
        };
        
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return 0;
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(snapshot, &pe32)) {
            do {
                for (const auto& target : legitimateTargets) {
                    if (_stricmp(pe32.szExeFile, target.c_str()) == 0) {
                        CloseHandle(snapshot);
                        return pe32.th32ProcessID;
                    }
                }
            } while (Process32Next(snapshot, &pe32));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
    
    // Create a suspended svchost process for injection
    DWORD CreateSuspendedSvchost() {
        STARTUPINFOA si = { sizeof(si) };
        PROCESS_INFORMATION pi;
        
        // Use a legitimate svchost command line
        char cmdLine[] = "C:\\Windows\\System32\\svchost.exe -k netsvcs -p";
        
        if (CreateProcessA(nullptr, cmdLine, nullptr, nullptr, FALSE, 
                          CREATE_SUSPENDED | CREATE_NO_WINDOW, nullptr, nullptr, &si, &pi)) {
            CloseHandle(pi.hThread);
            targetProcess = pi.hProcess;
            return pi.dwProcessId;
        }
        
        return 0;
    }
    
    // Manual DLL injection technique
    bool InjectJoyToKey(DWORD processId) {
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) return false;
        
        // Allocate memory in target process
        PVOID remoteMemory = VirtualAllocEx(hProcess, nullptr, joyToKeyData.size(),
                                           MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        
        if (!remoteMemory) {
            CloseHandle(hProcess);
            return false;
        }
        
        // Write JoyToKey data to target process
        if (!WriteProcessMemory(hProcess, remoteMemory, joyToKeyData.data(), 
                               joyToKeyData.size(), nullptr)) {
            VirtualFreeEx(hProcess, remoteMemory, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Create remote thread to execute JoyToKey
        HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0,
                                           (LPTHREAD_START_ROUTINE)remoteMemory,
                                           nullptr, 0, nullptr);
        
        if (hThread) {
            std::cout << "[+] Successfully injected JoyToKey into PID " << processId << std::endl;
            CloseHandle(hThread);
            CloseHandle(hProcess);
            return true;
        }
        
        VirtualFreeEx(hProcess, remoteMemory, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        return false;
    }
    
    // DLL Hollowing - Replace legitimate DLL with JoyToKey
    bool DllHollowing(DWORD processId) {
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) return false;
        
        // Find a suitable DLL to replace (like a system DLL that's not critical)
        HMODULE hMods[1024];
        DWORD cbNeeded;
        
        if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded)) {
            for (unsigned int i = 0; i < (cbNeeded / sizeof(HMODULE)); i++) {
                char modName[MAX_PATH];
                if (GetModuleFileNameExA(hProcess, hMods[i], modName, sizeof(modName))) {
                    // Look for non-critical DLLs we can replace
                    if (strstr(modName, "version.dll") || strstr(modName, "winmm.dll")) {
                        // Unmap the original DLL and map our JoyToKey
                        MODULEINFO modInfo;
                        if (GetModuleInformation(hProcess, hMods[i], &modInfo, sizeof(modInfo))) {
                            // This is a simplified approach - full implementation would be more complex
                            if (WriteProcessMemory(hProcess, modInfo.lpBaseOfDll, 
                                                 joyToKeyData.data(), 
                                                 min(joyToKeyData.size(), modInfo.SizeOfImage), nullptr)) {
                                std::cout << "[+] Successfully replaced " << modName << " with JoyToKey" << std::endl;
                                CloseHandle(hProcess);
                                return true;
                            }
                        }
                    }
                }
            }
        }
        
        CloseHandle(hProcess);
        return false;
    }
    
    // Advanced stealth execution using temporary file
    bool AdvancedStealthExecution() {
        // Create a temporary file with random name
        char tempPath[MAX_PATH];
        GetTempPathA(MAX_PATH, tempPath);

        // Generate random filename that looks like a system file
        std::string randomName = "winlogon_" + std::to_string(GetTickCount()) + ".exe";
        strcat_s(tempPath, randomName.c_str());

        // Write JoyToKey data to temp file
        std::ofstream tempFile(tempPath, std::ios::binary);
        if (!tempFile.is_open()) return false;

        tempFile.write(reinterpret_cast<const char*>(joyToKeyData.data()), joyToKeyData.size());
        tempFile.close();

        // Set file attributes to make it look like a system file
        SetFileAttributesA(tempPath, FILE_ATTRIBUTE_SYSTEM | FILE_ATTRIBUTE_HIDDEN);

        // Create process with stealth flags
        STARTUPINFOA si = { sizeof(si) };
        PROCESS_INFORMATION pi;
        si.dwFlags = STARTF_USESHOWWINDOW;
        si.wShowWindow = SW_HIDE;

        if (CreateProcessA(tempPath, nullptr, nullptr, nullptr, FALSE,
                          CREATE_NEW_CONSOLE | CREATE_NO_WINDOW, nullptr, nullptr, &si, &pi)) {
            std::cout << "[+] Advanced stealth execution successful, PID: " << pi.dwProcessId << std::endl;

            // Wait a moment for process to start
            Sleep(2000);

            // Delete the temporary file after execution
            DeleteFileA(tempPath);

            CloseHandle(pi.hThread);
            CloseHandle(pi.hProcess);
            return true;
        }

        DeleteFileA(tempPath);
        return false;
    }
    
public:
    bool LoadJoyToKey(const std::string& path) {
        std::ifstream file(path, std::ios::binary);
        if (!file.is_open()) {
            std::cout << "[-] Failed to open JoyToKey.exe" << std::endl;
            return false;
        }
        
        file.seekg(0, std::ios::end);
        size_t fileSize = file.tellg();
        file.seekg(0, std::ios::beg);
        
        joyToKeyData.resize(fileSize);
        file.read(reinterpret_cast<char*>(joyToKeyData.data()), fileSize);
        file.close();
        
        std::cout << "[+] Loaded JoyToKey.exe (" << fileSize << " bytes)" << std::endl;
        return true;
    }
    
    bool ExecuteStealthMode() {
        std::cout << "[+] Attempting true stealth execution..." << std::endl;
        
        // Method 1: Advanced stealth execution (most reliable)
        std::cout << "[+] Trying advanced stealth execution..." << std::endl;
        if (AdvancedStealthExecution()) {
            return true;
        }
        
        // Method 2: DLL Hollowing
        std::cout << "[+] Trying DLL hollowing..." << std::endl;
        DWORD targetPid = FindLegitimateProcess();
        if (targetPid && DllHollowing(targetPid)) {
            return true;
        }
        
        // Method 3: Manual injection into svchost
        std::cout << "[+] Trying svchost injection..." << std::endl;
        targetPid = CreateSuspendedSvchost();
        if (targetPid && InjectJoyToKey(targetPid)) {
            // Resume the svchost process
            HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, targetPid);
            if (hProcess) {
                // Find the main thread and resume it
                HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
                if (snapshot != INVALID_HANDLE_VALUE) {
                    THREADENTRY32 te32;
                    te32.dwSize = sizeof(THREADENTRY32);
                    
                    if (Thread32First(snapshot, &te32)) {
                        do {
                            if (te32.th32OwnerProcessID == targetPid) {
                                HANDLE hThread = OpenThread(THREAD_SUSPEND_RESUME, FALSE, te32.th32ThreadID);
                                if (hThread) {
                                    ResumeThread(hThread);
                                    CloseHandle(hThread);
                                    break;
                                }
                            }
                        } while (Thread32Next(snapshot, &te32));
                    }
                    CloseHandle(snapshot);
                }
                CloseHandle(hProcess);
            }
            return true;
        }
        
        std::cout << "[-] All stealth methods failed" << std::endl;
        return false;
    }
    
    void MonitorExecution() {
        std::cout << "[+] JoyToKey is now running in true stealth mode!" << std::endl;
        std::cout << "[+] It should be invisible to anti-cheat detection" << std::endl;
        std::cout << "[+] The process appears as a legitimate Windows system process" << std::endl;
        std::cout << "\nPress Enter to exit monitoring..." << std::endl;
        std::cin.get();
    }
};

int main() {
    std::cout << "=== True Stealth JoyToKey Injector ===" << std::endl;
    std::cout << "Advanced Anti-Cheat Bypass Technology" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    TrueStealth stealth;
    
    if (!stealth.LoadJoyToKey("JoyToKey.exe")) {
        std::cout << "[-] Failed to load JoyToKey.exe" << std::endl;
        std::cout << "[-] Make sure JoyToKey.exe is in the same directory" << std::endl;
        return -1;
    }
    
    if (stealth.ExecuteStealthMode()) {
        std::cout << "\n[SUCCESS] JoyToKey is now running in true stealth mode!" << std::endl;
        stealth.MonitorExecution();
    } else {
        std::cout << "\n[FAILED] Could not execute JoyToKey in stealth mode" << std::endl;
        std::cout << "Try running as Administrator for better success rate" << std::endl;
        return -1;
    }
    
    return 0;
}
