@echo off 
echo [+] Cleaning up stealth traces... 
 
REM Kill any running processes 
taskkill /f /im stealth_loader.exe >nul 2>&1 
taskkill /f /im JoyToKey.exe >nul 2>&1 
 
REM Clean up files 
if exist "stealth_output" rmdir /s /q stealth_output 
 
REM Clean up registry (requires admin) 
net session >nul 2>&1 
if %errorlevel% equ 0 ( 
    echo [+] Cleaning registry entries... 
    reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths" /f >nul 2>&1 
) else ( 
    echo [!] Admin privileges required for full cleanup 
) 
 
echo [+] Cleanup completed 
pause 
