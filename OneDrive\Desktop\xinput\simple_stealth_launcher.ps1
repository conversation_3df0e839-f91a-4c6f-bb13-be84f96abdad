# Simple but Effective JoyTo<PERSON>ey Stealth Launcher
# This version focuses on actually running <PERSON>To<PERSON>ey with basic stealth

param(
    [string]$JoyToKeyPath = "JoyToKey.exe"
)

Write-Host "=== Simple JoyToKey Stealth Launcher ===" -ForegroundColor Cyan
Write-Host "Security Research Tool - Reliable Execution" -ForegroundColor Yellow
Write-Host ""

# Check if <PERSON>T<PERSON><PERSON><PERSON> exists
if (-not (Test-Path $JoyToKeyPath)) {
    Write-Host "[ERROR] JoyToKey.exe not found!" -ForegroundColor Red
    Write-Host "Please ensure JoyToKey.exe is in the current directory." -ForegroundColor Red
    exit 1
}

Write-Host "[+] Found JoyToKey.exe" -ForegroundColor Green
$joySize = (Get-Item $JoyToKeyPath).Length
Write-Host "[+] File size: $joySize bytes" -ForegroundColor Green

# Function to check if <PERSON><PERSON><PERSON><PERSON><PERSON> is already running
function Test-JoyToKeyRunning {
    $processes = Get-Process | Where-Object { 
        $_.ProcessName -like "*joy*" -or 
        $_.MainWindowTitle -like "*JoyToKey*" -or
        ($_.Path -and $_.Path -like "*JoyToKey*")
    }
    return $processes.Count -gt 0
}

# Function to launch JoyToKey with stealth techniques
function Start-JoyToKeyStealth {
    Write-Host "[+] Launching JoyToKey with stealth techniques..." -ForegroundColor Yellow
    
    try {
        # Method 1: Hidden window execution
        Write-Host "    - Attempting hidden window execution..." -ForegroundColor Gray
        
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = (Resolve-Path $JoyToKeyPath).Path
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Hidden
        $startInfo.CreateNoWindow = $true
        $startInfo.UseShellExecute = $false
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        
        if ($process -and -not $process.HasExited) {
            Write-Host "[SUCCESS] JoyToKey launched successfully!" -ForegroundColor Green
            Write-Host "    - Process ID: $($process.Id)" -ForegroundColor Green
            Write-Host "    - Process Name: $($process.ProcessName)" -ForegroundColor Green
            
            # Wait a moment for the process to initialize
            Start-Sleep -Seconds 2
            
            # Check if it's still running
            if (-not $process.HasExited) {
                Write-Host "    - Process is running and stable" -ForegroundColor Green
                return $process
            } else {
                Write-Host "    - Process exited immediately" -ForegroundColor Yellow
            }
        }
    }
    catch {
        Write-Host "    - Hidden execution failed: $_" -ForegroundColor Yellow
    }
    
    # Method 2: WMI execution
    Write-Host "    - Attempting WMI execution..." -ForegroundColor Gray
    
    try {
        $fullPath = (Resolve-Path $JoyToKeyPath).Path
        $result = Invoke-WmiMethod -Class Win32_Process -Name Create -ArgumentList $fullPath
        
        if ($result.ReturnValue -eq 0) {
            Write-Host "[SUCCESS] JoyToKey launched via WMI!" -ForegroundColor Green
            Write-Host "    - Process ID: $($result.ProcessId)" -ForegroundColor Green
            
            $process = Get-Process -Id $result.ProcessId -ErrorAction SilentlyContinue
            if ($process) {
                return $process
            }
        } else {
            Write-Host "    - WMI execution failed with code: $($result.ReturnValue)" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "    - WMI execution failed: $_" -ForegroundColor Yellow
    }
    
    # Method 3: PowerShell Start-Process with stealth
    Write-Host "    - Attempting PowerShell stealth execution..." -ForegroundColor Gray
    
    try {
        $process = Start-Process -FilePath $JoyToKeyPath -WindowStyle Hidden -PassThru
        
        if ($process -and -not $process.HasExited) {
            Write-Host "[SUCCESS] JoyToKey launched via PowerShell!" -ForegroundColor Green
            Write-Host "    - Process ID: $($process.Id)" -ForegroundColor Green
            return $process
        }
    }
    catch {
        Write-Host "    - PowerShell execution failed: $_" -ForegroundColor Yellow
    }
    
    # Method 4: Direct execution (fallback)
    Write-Host "    - Attempting direct execution (fallback)..." -ForegroundColor Gray
    
    try {
        $process = Start-Process -FilePath $JoyToKeyPath -PassThru
        
        if ($process) {
            Write-Host "[SUCCESS] JoyToKey launched directly!" -ForegroundColor Green
            Write-Host "    - Process ID: $($process.Id)" -ForegroundColor Green
            Write-Host "    - Note: This method is less stealthy" -ForegroundColor Yellow
            return $process
        }
    }
    catch {
        Write-Host "    - Direct execution failed: $_" -ForegroundColor Red
    }
    
    return $null
}

# Function to verify JoyToKey is working
function Test-JoyToKeyFunctionality {
    param($Process)
    
    Write-Host "[+] Verifying JoyToKey functionality..." -ForegroundColor Yellow
    
    # Check if process is still running
    if ($Process.HasExited) {
        Write-Host "[WARNING] JoyToKey process has exited!" -ForegroundColor Red
        return $false
    }
    
    # Check for JoyToKey configuration files
    $configFiles = @("JoyToKey.cfg", "*.cfg")
    $configFound = $false
    
    foreach ($pattern in $configFiles) {
        $files = Get-ChildItem -Path . -Name $pattern -ErrorAction SilentlyContinue
        if ($files) {
            Write-Host "    - Found config file(s): $($files -join ', ')" -ForegroundColor Green
            $configFound = $true
        }
    }
    
    if (-not $configFound) {
        Write-Host "    - No configuration files found in current directory" -ForegroundColor Yellow
        Write-Host "    - JoyToKey may be using default settings" -ForegroundColor Yellow
    }
    
    # Check memory usage (JoyToKey should use some memory)
    try {
        $memoryMB = [math]::Round($Process.WorkingSet / 1MB, 2)
        Write-Host "    - Memory usage: $memoryMB MB" -ForegroundColor Green
        
        if ($memoryMB -gt 1) {
            Write-Host "    - Memory usage looks normal for JoyToKey" -ForegroundColor Green
            return $true
        } else {
            Write-Host "    - Low memory usage - process might not be fully loaded" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "    - Could not check memory usage" -ForegroundColor Yellow
    }
    
    return $true
}

# Main execution
Write-Host "Starting JoyToKey stealth execution..." -ForegroundColor Cyan

# Check if already running
if (Test-JoyToKeyRunning) {
    Write-Host "[WARNING] JoyToKey appears to already be running!" -ForegroundColor Yellow
    $existing = Get-Process | Where-Object { $_.ProcessName -like "*joy*" }
    if ($existing) {
        Write-Host "Existing JoyToKey processes:" -ForegroundColor Yellow
        $existing | ForEach-Object {
            Write-Host "    - PID: $($_.Id), Name: $($_.ProcessName)" -ForegroundColor White
        }
    }
    
    $continue = Read-Host "Continue anyway? (y/N)"
    if ($continue -ne 'y' -and $continue -ne 'Y') {
        Write-Host "Execution cancelled." -ForegroundColor Gray
        exit 0
    }
}

# Launch JoyToKey
$joyProcess = Start-JoyToKeyStealth

if ($joyProcess) {
    Write-Host ""
    Write-Host "=== EXECUTION SUCCESSFUL ===" -ForegroundColor Green
    Write-Host "JoyToKey is now running!" -ForegroundColor Green
    Write-Host ""
    
    # Verify functionality
    $isWorking = Test-JoyToKeyFunctionality -Process $joyProcess
    
    if ($isWorking) {
        Write-Host "[+] JoyToKey appears to be functioning correctly" -ForegroundColor Green
    } else {
        Write-Host "[!] JoyToKey may not be functioning properly" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "=== TESTING INSTRUCTIONS ===" -ForegroundColor Cyan
    Write-Host "1. Connect a game controller" -ForegroundColor White
    Write-Host "2. Press controller buttons to test if they generate keyboard/mouse events" -ForegroundColor White
    Write-Host "3. If you have JoyToKey profiles (.cfg files), they should be loaded automatically" -ForegroundColor White
    Write-Host "4. Check Task Manager to see the JoyToKey process running" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Press Enter to stop monitoring (JoyToKey will continue running)..." -ForegroundColor Gray
    Read-Host
    
    Write-Host "JoyToKey is still running in the background." -ForegroundColor Green
    Write-Host "To stop it, use Task Manager or run: Stop-Process -Name '$($joyProcess.ProcessName)'" -ForegroundColor Gray
    
} else {
    Write-Host ""
    Write-Host "=== EXECUTION FAILED ===" -ForegroundColor Red
    Write-Host "Could not launch JoyToKey using any method." -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible issues:" -ForegroundColor Yellow
    Write-Host "- JoyToKey.exe is corrupted or incompatible" -ForegroundColor Yellow
    Write-Host "- Antivirus software is blocking execution" -ForegroundColor Yellow
    Write-Host "- Insufficient permissions" -ForegroundColor Yellow
    Write-Host "- Missing dependencies" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Try running as Administrator or check antivirus settings." -ForegroundColor Yellow
}
