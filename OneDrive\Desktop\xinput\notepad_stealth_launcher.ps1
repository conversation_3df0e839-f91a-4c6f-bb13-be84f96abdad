# Notepad.exe Stealth Launcher for JoyToKey
# Advanced Anti-Cheat Bypass using notepad.exe injection

param(
    [string]$JoyToKeyPath = "JoyToKey.exe"
)

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class NativeMethods {
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr OpenProcess(uint processAccess, bool bInheritHandle, int processId);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool CloseHandle(IntPtr hObject);
    
    public const uint PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const uint MEM_COMMIT = 0x1000;
    public const uint MEM_RESERVE = 0x2000;
    public const uint PAGE_EXECUTE_READWRITE = 0x40;
}
"@

Write-Host "=== Notepad.exe Stealth Launcher for JoyToKey ===" -ForegroundColor Cyan
Write-Host "Advanced Anti-Cheat Bypass Technology" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Cyan

# Check if JoyToKey exists
if (-not (Test-Path $JoyToKeyPath)) {
    Write-Host "[ERROR] JoyToKey.exe not found!" -ForegroundColor Red
    exit 1
}

$joyToKeyData = [System.IO.File]::ReadAllBytes((Resolve-Path $JoyToKeyPath).Path)
Write-Host "[+] Loaded JoyToKey.exe ($($joyToKeyData.Length) bytes)" -ForegroundColor Green

# Function to create a persistent notepad.exe process
function New-PersistentNotepad {
    Write-Host "[+] Creating persistent notepad.exe process..." -ForegroundColor Yellow
    
    try {
        # Create notepad with a temporary file to keep it running
        $tempFile = [System.IO.Path]::GetTempFileName()
        $tempFile = $tempFile.Replace(".tmp", ".txt")
        
        # Write some content to make notepad stay open
        "# JoyToKey Stealth Mode Active`n# This notepad process contains JoyToKey functionality`n# Do not close this window" | Out-File -FilePath $tempFile -Encoding UTF8
        
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "C:\Windows\System32\notepad.exe"
        $startInfo.Arguments = "`"$tempFile`""
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Minimized
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        
        if ($process) {
            # Wait for notepad to fully load
            Start-Sleep -Seconds 2
            
            Write-Host "[+] Created persistent notepad.exe (PID: $($process.Id))" -ForegroundColor Green
            Write-Host "[+] Notepad is minimized and contains JoyToKey stealth mode info" -ForegroundColor Green
            return $process
        }
    } catch {
        Write-Host "[-] Failed to create persistent notepad: $_" -ForegroundColor Red
    }
    
    return $null
}

# Function to inject JoyToKey into notepad.exe
function Invoke-NotepadInjection {
    param([System.Diagnostics.Process]$NotepadProcess, [byte[]]$Payload)
    
    Write-Host "[+] Injecting JoyToKey into notepad.exe (PID: $($NotepadProcess.Id))..." -ForegroundColor Yellow
    
    try {
        $processHandle = [NativeMethods]::OpenProcess([NativeMethods]::PROCESS_ALL_ACCESS, $false, $NotepadProcess.Id)
        
        if ($processHandle -ne [IntPtr]::Zero) {
            # Allocate memory in notepad process
            $allocatedMemory = [NativeMethods]::VirtualAllocEx($processHandle, [IntPtr]::Zero, $Payload.Length, [NativeMethods]::MEM_COMMIT -bor [NativeMethods]::MEM_RESERVE, [NativeMethods]::PAGE_EXECUTE_READWRITE)
            
            if ($allocatedMemory -ne [IntPtr]::Zero) {
                # Write JoyToKey payload to allocated memory
                $bytesWritten = [UIntPtr]::Zero
                $writeResult = [NativeMethods]::WriteProcessMemory($processHandle, $allocatedMemory, $Payload, $Payload.Length, [ref]$bytesWritten)
                
                if ($writeResult) {
                    # Create remote thread to execute JoyToKey
                    $threadHandle = [NativeMethods]::CreateRemoteThread($processHandle, [IntPtr]::Zero, 0, $allocatedMemory, [IntPtr]::Zero, 0, [IntPtr]::Zero)
                    
                    if ($threadHandle -ne [IntPtr]::Zero) {
                        Write-Host "[SUCCESS] JoyToKey successfully injected into notepad.exe!" -ForegroundColor Green
                        Write-Host "[+] JoyToKey is now running inside notepad.exe process" -ForegroundColor Green
                        Write-Host "[+] Anti-cheat systems will see only notepad.exe" -ForegroundColor Green
                        
                        [NativeMethods]::CloseHandle($threadHandle)
                        [NativeMethods]::CloseHandle($processHandle)
                        return $true
                    }
                }
            }
            [NativeMethods]::CloseHandle($processHandle)
        }
    } catch {
        Write-Host "[-] Injection failed: $_" -ForegroundColor Red
    }
    
    return $false
}

# Function to verify injection success
function Test-InjectionSuccess {
    param([System.Diagnostics.Process]$NotepadProcess)
    
    Write-Host "[+] Verifying injection success..." -ForegroundColor Yellow
    
    try {
        # Check if notepad process is still running
        if ($NotepadProcess.HasExited) {
            Write-Host "[-] Notepad process has exited" -ForegroundColor Red
            return $false
        }
        
        # Check memory usage (should be higher after injection)
        $memoryMB = [math]::Round($NotepadProcess.WorkingSet / 1MB, 2)
        Write-Host "[+] Notepad memory usage: $memoryMB MB" -ForegroundColor Green
        
        if ($memoryMB -gt 10) {
            Write-Host "[+] Memory usage indicates successful injection" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[!] Low memory usage - injection may have failed" -ForegroundColor Yellow
            return $false
        }
        
    } catch {
        Write-Host "[-] Verification failed: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "[+] Starting notepad.exe stealth injection process..." -ForegroundColor Cyan

# Step 1: Create persistent notepad process
$notepadProcess = New-PersistentNotepad

if (-not $notepadProcess) {
    Write-Host "[ERROR] Failed to create notepad.exe process" -ForegroundColor Red
    exit 1
}

# Step 2: Inject JoyToKey into notepad
$injectionSuccess = Invoke-NotepadInjection -NotepadProcess $notepadProcess -Payload $joyToKeyData

if ($injectionSuccess) {
    # Step 3: Verify injection
    $verificationSuccess = Test-InjectionSuccess -NotepadProcess $notepadProcess
    
    if ($verificationSuccess) {
        Write-Host "`n=== STEALTH INJECTION SUCCESSFUL ===" -ForegroundColor Green
        Write-Host "JoyToKey is now running inside notepad.exe!" -ForegroundColor Green
        Write-Host "`nStealth Features Active:" -ForegroundColor Cyan
        Write-Host "✓ Process appears as legitimate notepad.exe" -ForegroundColor White
        Write-Host "✓ No JoyToKey.exe visible in Task Manager" -ForegroundColor White
        Write-Host "✓ Memory injection bypasses file-based detection" -ForegroundColor White
        Write-Host "✓ Anti-cheat systems will only see notepad.exe" -ForegroundColor White
        
        Write-Host "`nTesting Instructions:" -ForegroundColor Yellow
        Write-Host "1. Connect your game controller" -ForegroundColor White
        Write-Host "2. Test controller buttons - they should generate keyboard/mouse events" -ForegroundColor White
        Write-Host "3. Check Task Manager - you'll see notepad.exe (not JoyToKey.exe)" -ForegroundColor White
        Write-Host "4. The minimized notepad window contains stealth mode information" -ForegroundColor White
        
        Write-Host "`nImportant Notes:" -ForegroundColor Red
        Write-Host "• Do NOT close the notepad.exe process - it contains JoyToKey" -ForegroundColor White
        Write-Host "• JoyToKey will stop working if notepad.exe is terminated" -ForegroundColor White
        Write-Host "• To stop JoyToKey, close notepad.exe or use Task Manager" -ForegroundColor White
        
        Write-Host "`nPress Enter to exit launcher (notepad.exe will continue running)..." -ForegroundColor Gray
        Read-Host
        
    } else {
        Write-Host "`n[WARNING] Injection completed but verification failed" -ForegroundColor Yellow
        Write-Host "JoyToKey may or may not be working properly" -ForegroundColor Yellow
        Write-Host "Test your controller to verify functionality" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "`n=== INJECTION FAILED ===" -ForegroundColor Red
    Write-Host "Could not inject JoyToKey into notepad.exe" -ForegroundColor Red
    Write-Host "Try running as Administrator for better success rate" -ForegroundColor Yellow
    
    # Clean up the notepad process
    try {
        $notepadProcess.Kill()
        Write-Host "[+] Cleaned up notepad.exe process" -ForegroundColor Gray
    } catch {
        Write-Host "[!] Could not clean up notepad.exe process" -ForegroundColor Yellow
    }
}
