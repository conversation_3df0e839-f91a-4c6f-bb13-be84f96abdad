# Notepad.exe Stealth Launcher for JoyToKey
# Advanced Anti-Cheat Bypass using notepad.exe injection

param(
    [string]$JoyToKeyPath = "JoyToKey.exe"
)

Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;

public class NativeMethods {
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr OpenProcess(uint processAccess, bool bInheritHandle, int processId);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool CloseHandle(IntPtr hObject);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr GetProcAddress(IntPtr hModule, string procName);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr GetModuleHandle(string lpModuleName);

    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint dwSize, out UIntPtr lpNumberOfBytesRead);

    [DllImport("ntdll.dll", SetLastError = true)]
    public static extern int NtUnmapViewOfSection(IntPtr ProcessHandle, IntPtr BaseAddress);

    public const uint PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const uint MEM_COMMIT = 0x1000;
    public const uint MEM_RESERVE = 0x2000;
    public const uint PAGE_EXECUTE_READWRITE = 0x40;
    public const uint PAGE_READWRITE = 0x04;
}
"@

Write-Host "=== Notepad.exe Stealth Launcher for JoyToKey ===" -ForegroundColor Cyan
Write-Host "Advanced Anti-Cheat Bypass Technology" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Cyan

# Check if JoyToKey exists
if (-not (Test-Path $JoyToKeyPath)) {
    Write-Host "[ERROR] JoyToKey.exe not found!" -ForegroundColor Red
    exit 1
}

$joyToKeyData = [System.IO.File]::ReadAllBytes((Resolve-Path $JoyToKeyPath).Path)
Write-Host "[+] Loaded JoyToKey.exe ($($joyToKeyData.Length) bytes)" -ForegroundColor Green

# Function to create a persistent notepad.exe process
function New-PersistentNotepad {
    Write-Host "[+] Creating persistent notepad.exe process..." -ForegroundColor Yellow
    
    try {
        # Create notepad with a temporary file to keep it running
        $tempFile = [System.IO.Path]::GetTempFileName()
        $tempFile = $tempFile.Replace(".tmp", ".txt")
        
        # Write some content to make notepad stay open
        "# JoyToKey Stealth Mode Active`n# This notepad process contains JoyToKey functionality`n# Do not close this window" | Out-File -FilePath $tempFile -Encoding UTF8
        
        $startInfo = New-Object System.Diagnostics.ProcessStartInfo
        $startInfo.FileName = "C:\Windows\System32\notepad.exe"
        $startInfo.Arguments = "`"$tempFile`""
        $startInfo.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Minimized
        
        $process = [System.Diagnostics.Process]::Start($startInfo)
        
        if ($process) {
            # Wait for notepad to fully load
            Start-Sleep -Seconds 2
            
            Write-Host "[+] Created persistent notepad.exe (PID: $($process.Id))" -ForegroundColor Green
            Write-Host "[+] Notepad is minimized and contains JoyToKey stealth mode info" -ForegroundColor Green
            return $process
        }
    } catch {
        Write-Host "[-] Failed to create persistent notepad: $_" -ForegroundColor Red
    }
    
    return $null
}

# Function to perform manual PE loading and execution
function Invoke-ManualPELoader {
    param([System.Diagnostics.Process]$NotepadProcess, [byte[]]$PEData)

    Write-Host "[+] Performing manual PE loading into notepad.exe (PID: $($NotepadProcess.Id))..." -ForegroundColor Yellow

    try {
        $processHandle = [NativeMethods]::OpenProcess([NativeMethods]::PROCESS_ALL_ACCESS, $false, $NotepadProcess.Id)

        if ($processHandle -ne [IntPtr]::Zero) {
            # Parse PE headers
            $dosHeader = [System.BitConverter]::ToUInt16($PEData, 0)
            if ($dosHeader -ne 0x5A4D) { # "MZ"
                Write-Host "[-] Invalid PE file - missing MZ signature" -ForegroundColor Red
                return $false
            }

            $peOffset = [System.BitConverter]::ToInt32($PEData, 60)
            $peSignature = [System.BitConverter]::ToUInt32($PEData, $peOffset)
            if ($peSignature -ne 0x00004550) { # "PE\0\0"
                Write-Host "[-] Invalid PE file - missing PE signature" -ForegroundColor Red
                return $false
            }

            # Get image size from optional header
            $imageSizeOffset = $peOffset + 24 + 56 # PE + COFF + OptionalHeader.SizeOfImage
            $imageSize = [System.BitConverter]::ToUInt32($PEData, $imageSizeOffset)

            # Get entry point
            $entryPointOffset = $peOffset + 24 + 16 # PE + COFF + OptionalHeader.AddressOfEntryPoint
            $entryPoint = [System.BitConverter]::ToUInt32($PEData, $entryPointOffset)

            Write-Host "[+] PE Analysis: ImageSize=$imageSize, EntryPoint=0x$($entryPoint.ToString('X8'))" -ForegroundColor Green

            # Allocate memory for the entire PE image
            $imageBase = [NativeMethods]::VirtualAllocEx($processHandle, [IntPtr]::Zero, $imageSize, [NativeMethods]::MEM_COMMIT -bor [NativeMethods]::MEM_RESERVE, [NativeMethods]::PAGE_EXECUTE_READWRITE)

            if ($imageBase -ne [IntPtr]::Zero) {
                Write-Host "[+] Allocated memory at 0x$($imageBase.ToString('X8')) for PE image" -ForegroundColor Green

                # Write PE headers
                $bytesWritten = [UIntPtr]::Zero
                $headerSize = 4096 # Standard header size
                $writeResult = [NativeMethods]::WriteProcessMemory($processHandle, $imageBase, $PEData[0..($headerSize-1)], $headerSize, [ref]$bytesWritten)

                if ($writeResult) {
                    Write-Host "[+] PE headers written successfully" -ForegroundColor Green

                    # Parse and write sections
                    $numberOfSections = [System.BitConverter]::ToUInt16($PEData, $peOffset + 6)
                    $sectionHeaderOffset = $peOffset + 24 + 224 # PE + COFF + OptionalHeader

                    Write-Host "[+] Processing $numberOfSections sections..." -ForegroundColor Green

                    for ($i = 0; $i -lt $numberOfSections; $i++) {
                        $sectionOffset = $sectionHeaderOffset + ($i * 40)
                        $virtualAddress = [System.BitConverter]::ToUInt32($PEData, $sectionOffset + 12)
                        $sizeOfRawData = [System.BitConverter]::ToUInt32($PEData, $sectionOffset + 16)
                        $pointerToRawData = [System.BitConverter]::ToUInt32($PEData, $sectionOffset + 20)

                        if ($sizeOfRawData -gt 0 -and $pointerToRawData -gt 0) {
                            $sectionData = $PEData[$pointerToRawData..($pointerToRawData + $sizeOfRawData - 1)]
                            $sectionAddress = [IntPtr]::Add($imageBase, $virtualAddress)

                            $sectionWritten = [UIntPtr]::Zero
                            $sectionResult = [NativeMethods]::WriteProcessMemory($processHandle, $sectionAddress, $sectionData, $sizeOfRawData, [ref]$sectionWritten)

                            if ($sectionResult) {
                                Write-Host "[+] Section $i written at offset 0x$($virtualAddress.ToString('X8'))" -ForegroundColor Green
                            }
                        }
                    }

                    # Create loader stub to properly initialize and execute JoyToKey
                    $loaderStub = @"
using System;
using System.Runtime.InteropServices;

public class JoyToKeyLoader {
    [DllImport("kernel32.dll")]
    public static extern IntPtr LoadLibrary(string lpFileName);

    [DllImport("kernel32.dll")]
    public static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

    public delegate int MainFunction();

    public static void Execute(IntPtr baseAddress, uint entryPoint) {
        try {
            IntPtr entryAddress = new IntPtr(baseAddress.ToInt64() + entryPoint);
            MainFunction main = (MainFunction)Marshal.GetDelegateForFunctionPointer(entryAddress, typeof(MainFunction));
            main();
        } catch (Exception ex) {
            // Silent execution
        }
    }
}
"@

                    # Calculate entry point address
                    $entryAddress = [IntPtr]::Add($imageBase, $entryPoint)

                    # Create remote thread at entry point
                    $threadHandle = [NativeMethods]::CreateRemoteThread($processHandle, [IntPtr]::Zero, 0, $entryAddress, [IntPtr]::Zero, 0, [IntPtr]::Zero)

                    if ($threadHandle -ne [IntPtr]::Zero) {
                        Write-Host "[SUCCESS] JoyToKey PE loaded and executed in notepad.exe!" -ForegroundColor Green
                        Write-Host "[+] Entry point thread created at 0x$($entryAddress.ToString('X8'))" -ForegroundColor Green
                        Write-Host "[+] JoyToKey should now be fully functional" -ForegroundColor Green

                        [NativeMethods]::CloseHandle($threadHandle)
                        [NativeMethods]::CloseHandle($processHandle)
                        return $true
                    } else {
                        Write-Host "[-] Failed to create remote thread at entry point" -ForegroundColor Red
                    }
                } else {
                    Write-Host "[-] Failed to write PE headers" -ForegroundColor Red
                }
            } else {
                Write-Host "[-] Failed to allocate memory for PE image" -ForegroundColor Red
            }

            [NativeMethods]::CloseHandle($processHandle)
        }
    } catch {
        Write-Host "[-] Manual PE loading failed: $_" -ForegroundColor Red
    }

    return $false
}

# Function to verify injection success
function Test-InjectionSuccess {
    param([System.Diagnostics.Process]$NotepadProcess)
    
    Write-Host "[+] Verifying injection success..." -ForegroundColor Yellow
    
    try {
        # Check if notepad process is still running
        if ($NotepadProcess.HasExited) {
            Write-Host "[-] Notepad process has exited" -ForegroundColor Red
            return $false
        }
        
        # Check memory usage (should be higher after injection)
        $memoryMB = [math]::Round($NotepadProcess.WorkingSet / 1MB, 2)
        Write-Host "[+] Notepad memory usage: $memoryMB MB" -ForegroundColor Green
        
        if ($memoryMB -gt 10) {
            Write-Host "[+] Memory usage indicates successful injection" -ForegroundColor Green
            return $true
        } else {
            Write-Host "[!] Low memory usage - injection may have failed" -ForegroundColor Yellow
            return $false
        }
        
    } catch {
        Write-Host "[-] Verification failed: $_" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "[+] Starting notepad.exe stealth injection process..." -ForegroundColor Cyan

# Step 1: Create persistent notepad process
$notepadProcess = New-PersistentNotepad

if (-not $notepadProcess) {
    Write-Host "[ERROR] Failed to create notepad.exe process" -ForegroundColor Red
    exit 1
}

# Step 2: Perform manual PE loading of JoyToKey into notepad
$injectionSuccess = Invoke-ManualPELoader -NotepadProcess $notepadProcess -PEData $joyToKeyData

if ($injectionSuccess) {
    # Step 3: Verify injection
    $verificationSuccess = Test-InjectionSuccess -NotepadProcess $notepadProcess
    
    if ($verificationSuccess) {
        Write-Host "`n=== STEALTH INJECTION SUCCESSFUL ===" -ForegroundColor Green
        Write-Host "JoyToKey is now running inside notepad.exe!" -ForegroundColor Green
        Write-Host "`nStealth Features Active:" -ForegroundColor Cyan
        Write-Host "✓ Process appears as legitimate notepad.exe" -ForegroundColor White
        Write-Host "✓ No JoyToKey.exe visible in Task Manager" -ForegroundColor White
        Write-Host "✓ Memory injection bypasses file-based detection" -ForegroundColor White
        Write-Host "✓ Anti-cheat systems will only see notepad.exe" -ForegroundColor White
        
        Write-Host "`nTesting Instructions:" -ForegroundColor Yellow
        Write-Host "1. Connect your game controller" -ForegroundColor White
        Write-Host "2. Test controller buttons - they should generate keyboard/mouse events" -ForegroundColor White
        Write-Host "3. Check Task Manager - you'll see notepad.exe (not JoyToKey.exe)" -ForegroundColor White
        Write-Host "4. The minimized notepad window contains stealth mode information" -ForegroundColor White
        
        Write-Host "`nImportant Notes:" -ForegroundColor Red
        Write-Host "• Do NOT close the notepad.exe process - it contains JoyToKey" -ForegroundColor White
        Write-Host "• JoyToKey will stop working if notepad.exe is terminated" -ForegroundColor White
        Write-Host "• To stop JoyToKey, close notepad.exe or use Task Manager" -ForegroundColor White
        
        Write-Host "`nPress Enter to exit launcher (notepad.exe will continue running)..." -ForegroundColor Gray
        Read-Host
        
    } else {
        Write-Host "`n[WARNING] Injection completed but verification failed" -ForegroundColor Yellow
        Write-Host "JoyToKey may or may not be working properly" -ForegroundColor Yellow
        Write-Host "Test your controller to verify functionality" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "`n=== INJECTION FAILED ===" -ForegroundColor Red
    Write-Host "Could not inject JoyToKey into notepad.exe" -ForegroundColor Red
    Write-Host "Try running as Administrator for better success rate" -ForegroundColor Yellow
    
    # Clean up the notepad process
    try {
        $notepadProcess.Kill()
        Write-Host "[+] Cleaned up notepad.exe process" -ForegroundColor Gray
    } catch {
        Write-Host "[!] Could not clean up notepad.exe process" -ForegroundColor Yellow
    }
}
