@echo off 
echo [+] Launching Advanced Stealth Framework 
echo [!] Ensure you have administrative privileges for full functionality 
echo. 
REM Check for admin privileges 
net session >nul 2>&1 
if %errorlevel% neq 0 ( 
    echo [!] Warning: Not running as administrator 
    echo [!] Some features may not work properly 
    echo. 
) 
 
echo Choose execution method: 
echo 1. Native C++ Stealth Loader 
echo 2. PowerShell Advanced Stealth 
echo 3. Both methods 
echo. 
set /p choice="Enter choice (1-3): " 
 
if "%choice%"=="1" goto native 
if "%choice%"=="2" goto powershell 
if "%choice%"=="3" goto both 
goto end 
 
:native 
echo [+] Launching Native C++ Stealth Loader... 
stealth_loader.exe 
goto end 
 
:powershell 
echo [+] Launching PowerShell Advanced Stealth... 
powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1 
goto end 
 
:both 
echo [+] Launching both methods... 
start /b stealth_loader.exe 
timeout /t 2 /nobreak >nul 
powershell -ExecutionPolicy Bypass -File advanced_stealth.ps1 
goto end 
 
:end 
echo [+] Execution completed 
pause 
